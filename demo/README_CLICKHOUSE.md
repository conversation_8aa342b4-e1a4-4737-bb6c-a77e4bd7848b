# ClickHouse集成指南

## 概述

本项目集成了ClickHouse作为时序数据库，用于存储和查询Prometheus监控指标数据。ClickHouse提供了高性能的数据插入和查询能力，特别适合处理大量的时序数据。

## 功能特性

### 核心功能
- **高性能数据插入**: 支持批量插入，自动缓冲和异步处理
- **灵活数据查询**: 提供多种查询接口，支持时间范围、指标过滤等
- **实时统计分析**: 基于物化视图的实时聚合统计
- **数据生命周期管理**: 自动TTL策略，数据压缩优化
- **连接池管理**: HikariCP连接池，确保高并发性能
- **健康监控**: 完整的健康检查和监控指标

### 技术特性
- **MergeTree表引擎**: 优化的存储和查询性能
- **数据压缩**: ZSTD压缩算法，节省存储空间
- **分区策略**: 按日期分区，便于数据管理和查询优化
- **索引优化**: 跳数索引和投影索引，提升查询速度
- **物化视图**: 自动聚合统计，支持小时级和日级汇总

## 快速开始

### 1. 环境准备

#### 安装ClickHouse
```bash
# Docker方式安装
docker run -d --name clickhouse-server \
  -p 8123:8123 -p 9000:9000 \
  --ulimit nofile=262144:262144 \
  clickhouse/clickhouse-server:latest

# 或使用docker-compose
version: '3.8'
services:
  clickhouse:
    image: clickhouse/clickhouse-server:latest
    ports:
      - "8123:8123"
      - "9000:9000"
    environment:
      CLICKHOUSE_DB: metrics_db
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
    volumes:
      - clickhouse_data:/var/lib/clickhouse
    ulimits:
      nofile:
        soft: 262144
        hard: 262144

volumes:
  clickhouse_data:
```

#### 初始化数据库
```bash
# 连接到ClickHouse
clickhouse-client --host localhost --port 9000

# 执行schema.sql中的建表语句
```

### 2. 配置应用

#### application.yml配置
```yaml
spring:
  datasource:
    clickhouse:
      url: *******************************************
      username: default
      password: 
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        idle-timeout: 300000
        max-lifetime: 1800000
        connection-timeout: 30000

clickhouse:
  batch:
    size: 1000
    timeout: 5000
    max-wait-time: 10000
  query:
    timeout: 30000
    max-rows: 100000
```

### 3. 启动应用

```bash
# 编译项目
mvn clean compile

# 运行应用
mvn spring-boot:run

# 或打包后运行
mvn clean package
java -jar target/demo-0.0.1-SNAPSHOT.jar
```

## API接口

### 数据查询接口

#### 1. 查询指标数据
```http
GET /api/metrics/query?startTime=2024-01-01 00:00:00&endTime=2024-01-01 23:59:59&metricName=cpu_usage&page=1&size=100
```

#### 2. 查询最近指标
```http
GET /api/metrics/recent?metricName=memory_usage&hours=24&limit=100
```

#### 3. 查询统计信息
```http
GET /api/metrics/statistics?startTime=2024-01-01 00:00:00&endTime=2024-01-01 23:59:59&metricName=cpu_usage
```

#### 4. 查询热门指标
```http
GET /api/metrics/top?startTime=2024-01-01 00:00:00&endTime=2024-01-01 23:59:59&limit=10
```

### 管理接口

#### 1. 缓冲区状态
```http
GET /api/metrics/buffer/status
```

#### 2. 强制刷新缓冲区
```http
POST /api/metrics/buffer/flush
```

#### 3. 健康检查
```http
GET /api/metrics/health
```

## 数据模型

### 主表结构 (prometheus_metrics)
```sql
CREATE TABLE prometheus_metrics (
    timestamp DateTime64(3),
    job LowCardinality(String),
    job_type LowCardinality(String),
    app_id LowCardinality(String),
    name LowCardinality(String),
    instance LowCardinality(String),
    metric_name LowCardinality(String),
    metric_type LowCardinality(String),
    description String,
    labels_json String,
    value Decimal64(8),
    cluster_id LowCardinality(String),
    device_type LowCardinality(String),
    device_id String,
    ip IPv4,
    port UInt16,
    connect_port UInt16,
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMMDD(timestamp)
ORDER BY (timestamp, metric_name, job, app_id, instance)
TTL timestamp + INTERVAL 90 DAY;
```

### 聚合视图
- **metrics_hourly_stats**: 小时级聚合统计
- **metrics_daily_stats**: 日级聚合统计

## 性能优化

### 1. 批量插入优化
- 默认批量大小: 1000条记录
- 自动缓冲机制，达到批量大小或超时自动刷新
- 异步插入支持，避免阻塞主线程

### 2. 查询优化
- 合理的分区键和排序键设计
- 跳数索引加速过滤查询
- 投影索引优化特定查询模式
- 连接池复用，减少连接开销

### 3. 存储优化
- ZSTD压缩算法，压缩比高
- TTL自动清理过期数据
- 分区策略便于数据管理

## 监控和运维

### 1. 健康检查
```java
@Autowired
private ClickHouseHealthService healthService;

// 检查连接健康
HealthStatus status = healthService.checkHealth();

// 获取系统信息
SystemInfo info = healthService.getSystemInfo();

// 获取表统计
TableStats stats = healthService.getTableStats();
```

### 2. 性能监控
- 缓冲区使用率监控
- 查询响应时间统计
- 数据插入速率监控
- 连接池状态监控

### 3. 运维操作
```java
// 强制刷新缓冲区
clickHouseMetricsService.forceFlushBuffer();

// 优化表性能
healthService.optimizeTables();

// 获取缓冲区状态
BufferStatus status = clickHouseMetricsService.getBufferStatus();
```

## 故障排除

### 常见问题

#### 1. 连接失败
- 检查ClickHouse服务是否启动
- 验证连接配置是否正确
- 检查网络连通性和防火墙设置

#### 2. 插入性能问题
- 调整批量插入大小
- 检查缓冲区配置
- 监控连接池状态

#### 3. 查询超时
- 优化查询条件，减少扫描范围
- 检查索引是否生效
- 调整查询超时配置

### 日志分析
```bash
# 查看应用日志
tail -f logs/prometheus-receiver.log

# 查看ClickHouse日志
docker logs clickhouse-server
```

## 最佳实践

### 1. 数据设计
- 合理选择分区键，避免小分区
- 使用LowCardinality优化字符串字段
- 适当使用物化视图预聚合数据

### 2. 查询优化
- 尽量使用时间范围过滤
- 避免SELECT *，只查询需要的字段
- 合理使用LIMIT限制结果集大小

### 3. 运维管理
- 定期监控磁盘使用情况
- 设置合理的TTL策略
- 定期执行OPTIMIZE操作

## 扩展功能

### 1. 自定义聚合
可以根据业务需求创建自定义的物化视图：

```sql
CREATE MATERIALIZED VIEW custom_metrics_stats
ENGINE = SummingMergeTree()
ORDER BY (metric_name, app_id)
AS SELECT
    metric_name,
    app_id,
    count() as count,
    avg(value) as avg_value
FROM prometheus_metrics
GROUP BY metric_name, app_id;
```

### 2. 数据导出
支持将数据导出为多种格式：

```java
// 导出为CSV
List<ClickHouseMetric> metrics = queryService.queryMetrics(request);
// 实现CSV导出逻辑

// 导出为JSON
String json = objectMapper.writeValueAsString(metrics);
```

## 版本兼容性

- ClickHouse: 23.x+
- Spring Boot: 3.x
- Java: 17+
- JDBC Driver: 0.4.6+

## 许可证

本项目遵循MIT许可证。
