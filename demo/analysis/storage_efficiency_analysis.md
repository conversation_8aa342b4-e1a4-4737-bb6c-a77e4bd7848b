# ClickHouse标签存储效率分析

## 当前JSON存储方式的问题

### 存储开销分析

假设一个典型的Prometheus环境：
- 1000个服务实例
- 每个实例100个指标
- 每个指标平均5个标签
- 每分钟采集一次数据

**标签重复度分析：**
```
常见标签及其重复度：
- job: "api-server" (重复10万次)
- environment: "production" (重复50万次)  
- region: "us-west-1" (重复30万次)
- instance: "10.0.1.100:8080" (重复100次)
- version: "v1.2.3" (重复20万次)
```

**存储开销计算：**
```
JSON存储方式：
每个标签组合: {"job":"api-server","env":"prod","region":"us-west-1","instance":"10.0.1.100:8080","version":"v1.2.3"}
平均大小: ~120字节

每天数据量: 1000实例 × 100指标 × 1440分钟 = 1.44亿条记录
标签存储总量: 1.44亿 × 120字节 = 17.28GB/天

重复存储浪费:
- "job":"api-server" 重复存储 ~1.7GB
- "environment":"production" 重复存储 ~2.1GB  
- "region":"us-west-1" 重复存储 ~1.8GB
总浪费: ~60-70%的存储空间
```

### 压缩效果分析

ClickHouse的ZSTD压缩对JSON字符串有一定效果，但：
1. **压缩比有限**: JSON格式本身冗余度高，压缩比通常只有3-5倍
2. **查询开销**: 每次查询都需要解压缩和JSON解析
3. **索引限制**: JSON字段难以建立高效索引

## 标签字典方案的优势

### 存储空间节省
```
字典表存储:
- 标签键表: 平均每个键8字节
- 标签值表: 平均每个值16字节  
- 标签组合表: 每个组合32字节(4个8字节ID)

相同数据量存储:
- 标签键: 100个 × 8字节 = 800字节
- 标签值: 1000个 × 16字节 = 16KB
- 标签组合: 10万个 × 32字节 = 3.2MB
- 主表引用: 1.44亿 × 8字节 = 1.15GB

总存储: ~1.17GB vs 17.28GB (节省93%)
```

### 查询性能提升
1. **标签过滤**: 直接使用整数ID比较，比JSON字符串匹配快10-100倍
2. **索引效率**: 整数字段可以建立高效的跳数索引
3. **内存使用**: 标签字典可以完全加载到内存中
