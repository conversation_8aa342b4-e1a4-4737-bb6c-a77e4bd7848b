server:
  port: 9091

spring:
  application:
    name: prometheus-receiver

  # ClickHouse数据源配置
  datasource:
    clickhouse:
      url: *******************************************
      username: default
      password:
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        idle-timeout: 300000
        max-lifetime: 1800000
        connection-timeout: 30000
        validation-timeout: 5000
        leak-detection-threshold: 60000

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

logging:
  file:
    name: logs/prometheus-receiver.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      total-size-cap: 100MB
      max-history: 30
      clean-history-on-start: true

# ClickHouse相关配置
clickhouse:
  # 批量插入配置
  batch:
    size: 1000
    timeout: 5000
    max-wait-time: 10000
  # 查询配置
  query:
    timeout: 30000
    max-rows: 100000
  # 连接配置
  connection:
    socket-timeout: 30000
    connection-timeout: 10000
    max-retries: 3