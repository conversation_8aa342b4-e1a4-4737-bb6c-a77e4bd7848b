-- ClickHouse优化标签存储方案
-- 使用标签字典+引用ID的方式减少重复存储

-- 创建数据库
CREATE DATABASE IF NOT EXISTS metrics_db_optimized;
USE metrics_db_optimized;

-- 1. 标签键字典表
CREATE TABLE IF NOT EXISTS label_keys (
    id UInt32,
    key_name LowCardinality(String) CODEC(ZSTD),
    created_at DateTime DEFAULT now() CODEC(ZSTD)
) ENGINE = ReplacingMergeTree(created_at)
ORDER BY id
SETTINGS index_granularity = 8192;

-- 2. 标签值字典表  
CREATE TABLE IF NOT EXISTS label_values (
    id UInt64,
    key_id UInt32,
    value_text LowCardinality(String) CODEC(ZSTD),
    created_at DateTime DEFAULT now() CODEC(ZSTD)
) ENGINE = ReplacingMergeTree(created_at)
ORDER BY (key_id, id)
SETTINGS index_granularity = 8192;

-- 3. 标签组合表（标签集合的字典）
CREATE TABLE IF NOT EXISTS label_sets (
    id UInt64,
    label_pairs Array(Tuple(UInt32, UInt64)) CODEC(ZSTD), -- Array of (key_id, value_id)
    labels_hash UInt64 CODEC(ZSTD), -- 标签组合的哈希值，用于快速查找
    created_at DateTime DEFAULT now() CODEC(ZSTD)
) ENGINE = ReplacingMergeTree(created_at)
ORDER BY (labels_hash, id)
SETTINGS index_granularity = 8192;

-- 4. 优化后的主指标表
CREATE TABLE IF NOT EXISTS prometheus_metrics_optimized (
    -- 时间戳字段
    timestamp DateTime64(3) CODEC(Delta, ZSTD),
    
    -- 业务标识字段（保留常用字段以提升查询性能）
    job LowCardinality(String) CODEC(ZSTD),
    job_type LowCardinality(String) CODEC(ZSTD),
    app_id LowCardinality(String) CODEC(ZSTD),
    instance LowCardinality(String) CODEC(ZSTD),
    
    -- 指标基本信息
    metric_name LowCardinality(String) CODEC(ZSTD),
    metric_type LowCardinality(String) CODEC(ZSTD),
    
    -- 指标数值
    value Decimal64(8) CODEC(ZSTD),
    
    -- 标签集合引用ID
    label_set_id UInt64 CODEC(ZSTD),
    
    -- 数据插入时间
    created_at DateTime DEFAULT now() CODEC(ZSTD)
)
ENGINE = MergeTree()
PARTITION BY toYYYYMMDD(timestamp)
ORDER BY (timestamp, metric_name, job, app_id, instance, label_set_id)
TTL timestamp + INTERVAL 90 DAY
SETTINGS index_granularity = 8192;

-- 5. 标签查询优化的物化视图
CREATE MATERIALIZED VIEW IF NOT EXISTS metrics_with_labels_mv
ENGINE = MergeTree()
PARTITION BY toYYYYMMDD(timestamp)
ORDER BY (timestamp, metric_name, label_set_id)
TTL timestamp + INTERVAL 30 DAY
AS SELECT
    timestamp,
    metric_name,
    job,
    app_id,
    instance,
    value,
    label_set_id,
    ls.label_pairs as labels
FROM prometheus_metrics_optimized pm
LEFT JOIN label_sets ls ON pm.label_set_id = ls.id;

-- 6. 创建索引优化查询性能
ALTER TABLE prometheus_metrics_optimized ADD INDEX idx_label_set_id label_set_id TYPE bloom_filter GRANULARITY 1;
ALTER TABLE label_sets ADD INDEX idx_labels_hash labels_hash TYPE bloom_filter GRANULARITY 1;

-- 7. 标签键值快速查找视图
CREATE VIEW label_key_value_pairs AS
SELECT 
    lk.key_name,
    lv.value_text,
    lv.key_id,
    lv.id as value_id
FROM label_keys lk
JOIN label_values lv ON lk.id = lv.key_id;

-- 8. 预聚合统计表（按标签维度）
CREATE MATERIALIZED VIEW IF NOT EXISTS metrics_hourly_by_labels
ENGINE = SummingMergeTree()
PARTITION BY toYYYYMMDD(hour_timestamp)
ORDER BY (hour_timestamp, metric_name, label_set_id)
TTL hour_timestamp + INTERVAL 180 DAY
AS SELECT
    toStartOfHour(timestamp) as hour_timestamp,
    metric_name,
    label_set_id,
    count() as metric_count,
    avg(value) as avg_value,
    min(value) as min_value,
    max(value) as max_value,
    sum(value) as sum_value
FROM prometheus_metrics_optimized
GROUP BY hour_timestamp, metric_name, label_set_id;

-- 9. 常用标签组合的预计算表
CREATE MATERIALIZED VIEW IF NOT EXISTS popular_label_combinations
ENGINE = SummingMergeTree()
ORDER BY (metric_name, label_set_id, count)
AS SELECT
    metric_name,
    label_set_id,
    count() as count
FROM prometheus_metrics_optimized
GROUP BY metric_name, label_set_id
HAVING count > 1000; -- 只保留出现频率高的标签组合

-- 10. 标签基数统计表
CREATE MATERIALIZED VIEW IF NOT EXISTS label_cardinality_stats
ENGINE = ReplacingMergeTree()
ORDER BY (key_name, date)
AS SELECT
    lk.key_name,
    toDate(now()) as date,
    uniq(lv.value_text) as unique_values,
    count() as total_occurrences
FROM label_keys lk
JOIN label_values lv ON lk.id = lv.key_id
JOIN label_sets ls ON arrayExists(x -> x.1 = lk.id, ls.label_pairs)
GROUP BY lk.key_name;

-- 插入一些示例数据用于测试
INSERT INTO label_keys VALUES 
(1, 'job', now()),
(2, 'instance', now()),
(3, 'environment', now()),
(4, 'region', now()),
(5, 'version', now());

INSERT INTO label_values VALUES
(1, 1, 'api-server', now()),
(2, 1, 'web-server', now()),
(3, 2, '10.0.1.100:8080', now()),
(4, 2, '10.0.1.101:8080', now()),
(5, 3, 'production', now()),
(6, 3, 'staging', now()),
(7, 4, 'us-west-1', now()),
(8, 4, 'us-east-1', now()),
(9, 5, 'v1.2.3', now()),
(10, 5, 'v1.2.4', now());

-- 示例标签组合
INSERT INTO label_sets VALUES
(1, [(1,1), (2,3), (3,5), (4,7), (5,9)], cityHash64('job=api-server,instance=10.0.1.100:8080,environment=production,region=us-west-1,version=v1.2.3'), now()),
(2, [(1,2), (2,4), (3,5), (4,7), (5,9)], cityHash64('job=web-server,instance=10.0.1.101:8080,environment=production,region=us-west-1,version=v1.2.3'), now());
