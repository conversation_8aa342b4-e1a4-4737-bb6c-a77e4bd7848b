-- ClickHouse表结构设计
-- 基于项目接收的Prometheus监控指标数据

-- 创建数据库
CREATE DATABASE IF NOT EXISTS metrics_db;

-- 使用数据库
USE metrics_db;

-- 主要指标数据表
CREATE TABLE IF NOT EXISTS prometheus_metrics (
    -- 时间戳字段，作为主要分区键
    timestamp DateTime64(3) CODEC(Delta, ZSTD),
    
    -- 业务标识字段
    job LowCardinality(String) CODEC(ZSTD),
    job_type LowCardinality(String) CODEC(ZSTD),
    app_id LowCardinality(String) CODEC(ZSTD),
    name LowCardinality(String) CODEC(ZSTD),
    instance LowCardinality(String) CODEC(ZSTD),
    
    -- 指标基本信息
    metric_name LowCardinality(String) CODEC(ZSTD),
    metric_type LowCardinality(String) CODEC(ZSTD),
    description String CODEC(ZSTD),
    
    -- 标签信息（JSON格式存储）
    labels_json String CODEC(ZSTD),
    
    -- 指标数值
    value Decimal64(8) CODEC(ZSTD),
    
    -- 设备相关字段
    cluster_id LowCardinality(String) CODEC(ZSTD),
    device_type LowCardinality(String) CODEC(ZSTD),
    device_id String CODEC(ZSTD),
    ip IPv4 CODEC(ZSTD),
    port UInt16 CODEC(ZSTD),
    connect_port UInt16 CODEC(ZSTD),
    
    -- 数据插入时间（用于数据管理）
    created_at DateTime DEFAULT now() CODEC(ZSTD)
)
ENGINE = MergeTree()
-- 按天分区，便于数据管理和查询优化
PARTITION BY toYYYYMMDD(timestamp)
-- 排序键设计：时间戳 + 高基数字段 + 低基数字段
ORDER BY (timestamp, metric_name, job, app_id, instance)
-- TTL设置：数据保留90天
TTL timestamp + INTERVAL 90 DAY
-- 索引设置
SETTINGS index_granularity = 8192;

-- 创建物化视图用于实时聚合统计
CREATE MATERIALIZED VIEW IF NOT EXISTS metrics_hourly_stats
ENGINE = SummingMergeTree()
PARTITION BY toYYYYMMDD(hour_timestamp)
ORDER BY (hour_timestamp, metric_name, job, app_id)
TTL hour_timestamp + INTERVAL 180 DAY
AS SELECT
    toStartOfHour(timestamp) as hour_timestamp,
    metric_name,
    job,
    app_id,
    count() as metric_count,
    avg(value) as avg_value,
    min(value) as min_value,
    max(value) as max_value,
    sum(value) as sum_value
FROM prometheus_metrics
GROUP BY hour_timestamp, metric_name, job, app_id;

-- 创建日聚合统计表
CREATE MATERIALIZED VIEW IF NOT EXISTS metrics_daily_stats
ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(day_timestamp)
ORDER BY (day_timestamp, metric_name, job, app_id)
TTL day_timestamp + INTERVAL 365 DAY
AS SELECT
    toStartOfDay(timestamp) as day_timestamp,
    metric_name,
    job,
    app_id,
    count() as metric_count,
    avg(value) as avg_value,
    min(value) as min_value,
    max(value) as max_value,
    sum(value) as sum_value
FROM prometheus_metrics
GROUP BY day_timestamp, metric_name, job, app_id;

-- 创建设备状态表（用于设备监控）
CREATE TABLE IF NOT EXISTS device_status (
    timestamp DateTime64(3) CODEC(Delta, ZSTD),
    device_id String CODEC(ZSTD),
    device_type LowCardinality(String) CODEC(ZSTD),
    cluster_id LowCardinality(String) CODEC(ZSTD),
    ip IPv4 CODEC(ZSTD),
    port UInt16 CODEC(ZSTD),
    status LowCardinality(String) CODEC(ZSTD), -- online, offline, error
    last_seen DateTime DEFAULT now() CODEC(ZSTD),
    created_at DateTime DEFAULT now() CODEC(ZSTD)
)
ENGINE = ReplacingMergeTree(last_seen)
PARTITION BY toYYYYMMDD(timestamp)
ORDER BY (device_id, timestamp)
TTL timestamp + INTERVAL 30 DAY
SETTINGS index_granularity = 8192;

-- 创建索引以优化查询性能
-- 跳数索引用于快速过滤
ALTER TABLE prometheus_metrics ADD INDEX idx_metric_name metric_name TYPE bloom_filter GRANULARITY 1;
ALTER TABLE prometheus_metrics ADD INDEX idx_job job TYPE bloom_filter GRANULARITY 1;
ALTER TABLE prometheus_metrics ADD INDEX idx_app_id app_id TYPE bloom_filter GRANULARITY 1;
ALTER TABLE prometheus_metrics ADD INDEX idx_device_id device_id TYPE bloom_filter GRANULARITY 1;

-- 投影索引用于特定查询模式优化
ALTER TABLE prometheus_metrics ADD PROJECTION proj_by_metric (
    SELECT timestamp, metric_name, value, job, app_id
    ORDER BY (metric_name, timestamp)
);

-- 投影索引用于设备相关查询
ALTER TABLE prometheus_metrics ADD PROJECTION proj_by_device (
    SELECT timestamp, device_id, device_type, metric_name, value
    ORDER BY (device_id, timestamp)
);
