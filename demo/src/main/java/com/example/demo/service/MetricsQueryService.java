package com.example.demo.service;

import com.example.demo.dao.ClickHouseMetricsDao;
import com.example.demo.dto.MetricsQueryRequest;
import com.example.demo.dto.MetricsQueryResponse;
import com.example.demo.model.ClickHouseMetric;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 指标查询服务
 * 提供各种查询功能和聚合统计
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MetricsQueryService {

    private final ClickHouseMetricsService clickHouseMetricsService;
    private final ClickHouseMetricsDao clickHouseMetricsDao;

    /**
     * 根据查询条件查询指标数据
     */
    public MetricsQueryResponse queryMetrics(MetricsQueryRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 验证查询参数
            validateQueryRequest(request);
            
            // 查询指标数据
            List<ClickHouseMetric> metrics = clickHouseMetricsService.queryMetricsByTimeRange(
                request.getStartTime(),
                request.getEndTime(),
                request.getMetricName(),
                request.getJob(),
                request.getLimit()
            );

            // 构建响应
            MetricsQueryResponse.MetricsQueryResponseBuilder responseBuilder = MetricsQueryResponse.builder()
                .metrics(metrics)
                .executionTimeMs(System.currentTimeMillis() - startTime);

            // 添加分页信息（简化版，实际应该查询总数）
            MetricsQueryResponse.PageInfo pageInfo = MetricsQueryResponse.PageInfo.calculate(
                request.getPage(),
                request.getSize(),
                metrics.size() // 这里简化处理，实际应该查询总数
            );
            responseBuilder.pageInfo(pageInfo);

            // 如果需要统计信息
            if (request.isIncludeStats()) {
                List<ClickHouseMetricsDao.MetricStatistics> statistics = 
                    clickHouseMetricsService.getMetricStatistics(
                        request.getStartTime(),
                        request.getEndTime(),
                        request.getMetricName()
                    );
                responseBuilder.statistics(statistics);
            }

            MetricsQueryResponse response = responseBuilder.build();
            
            log.info("Query completed: found {} metrics in {}ms", 
                metrics.size(), response.getExecutionTimeMs());
            
            return response;
            
        } catch (Exception e) {
            log.error("Failed to query metrics", e);
            throw new RuntimeException("Failed to query metrics", e);
        }
    }

    /**
     * 查询最近的指标数据
     */
    public List<ClickHouseMetric> queryRecentMetrics(String metricName, String job, int hours, int limit) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusHours(hours);
        
        return clickHouseMetricsService.queryMetricsByTimeRange(startTime, endTime, metricName, job, limit);
    }

    /**
     * 查询指定时间段的指标统计
     */
    public List<ClickHouseMetricsDao.MetricStatistics> queryMetricStatistics(
            LocalDateTime startTime, LocalDateTime endTime, String metricName) {
        
        try {
            return clickHouseMetricsService.getMetricStatistics(startTime, endTime, metricName);
        } catch (Exception e) {
            log.error("Failed to query metric statistics", e);
            throw new RuntimeException("Failed to query metric statistics", e);
        }
    }

    /**
     * 查询热门指标（按数量排序）
     */
    public List<ClickHouseMetricsDao.MetricStatistics> queryTopMetrics(
            LocalDateTime startTime, LocalDateTime endTime, int limit) {
        
        try {
            List<ClickHouseMetricsDao.MetricStatistics> allStats = 
                clickHouseMetricsService.getMetricStatistics(startTime, endTime, null);
            
            // 按数量排序并限制结果
            return allStats.stream()
                .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                .limit(limit)
                .toList();
                
        } catch (Exception e) {
            log.error("Failed to query top metrics", e);
            throw new RuntimeException("Failed to query top metrics", e);
        }
    }

    /**
     * 查询指标趋势数据（按小时聚合）
     */
    public List<MetricTrend> queryMetricTrends(String metricName, String job, int days) {
        // 这里可以实现基于物化视图的趋势查询
        // 暂时返回空列表，实际实现需要查询hourly_stats表
        log.info("Querying metric trends for metric: {}, job: {}, days: {}", metricName, job, days);
        return List.of();
    }

    /**
     * 验证查询请求参数
     */
    private void validateQueryRequest(MetricsQueryRequest request) {
        if (request.getStartTime() == null) {
            throw new IllegalArgumentException("Start time is required");
        }
        
        if (request.getEndTime() == null) {
            throw new IllegalArgumentException("End time is required");
        }
        
        if (request.getStartTime().isAfter(request.getEndTime())) {
            throw new IllegalArgumentException("Start time must be before end time");
        }
        
        // 限制查询时间范围（例如最多查询30天）
        if (request.getStartTime().isBefore(request.getEndTime().minusDays(30))) {
            throw new IllegalArgumentException("Query time range cannot exceed 30 days");
        }
    }

    /**
     * 指标趋势数据
     */
    public static class MetricTrend {
        private LocalDateTime timestamp;
        private String metricName;
        private String job;
        private double avgValue;
        private double minValue;
        private double maxValue;
        private long count;

        // Getters and setters
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        public String getMetricName() { return metricName; }
        public void setMetricName(String metricName) { this.metricName = metricName; }
        public String getJob() { return job; }
        public void setJob(String job) { this.job = job; }
        public double getAvgValue() { return avgValue; }
        public void setAvgValue(double avgValue) { this.avgValue = avgValue; }
        public double getMinValue() { return minValue; }
        public void setMinValue(double minValue) { this.minValue = minValue; }
        public double getMaxValue() { return maxValue; }
        public void setMaxValue(double maxValue) { this.maxValue = maxValue; }
        public long getCount() { return count; }
        public void setCount(long count) { this.count = count; }
    }
}
