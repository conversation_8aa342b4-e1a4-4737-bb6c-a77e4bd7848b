package com.example.demo.service;

import cn.hutool.json.JSONUtil;
import com.example.demo.converter.MetricsConverter;
import com.example.demo.model.ClickHouseMetric;
import com.example.demo.model.MetricsRequest;
import com.example.demo.model.PrometheusMetric;
import com.example.demo.parser.PrometheusMetricsParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MetricsProcessingService {

    private final PrometheusMetricsParser parser;
    private final MetricsConverter converter;
    private final ClickHouseMetricsService clickHouseMetricsService;

    public void processMetrics(MetricsRequest request) {
        try {
            // 解析Prometheus格式的指标数据
            List<PrometheusMetric> prometheusMetrics = parser.parseMetrics(
                    request.getMetricsData(),
                    request.getJob(),
                    request.getJobType(),
                    request.getAppId(),
                    request.getName(),
                    request.getInstance()
            );
            log.info("Parsed {} metrics from request", prometheusMetrics.size());

            // 转换为ClickHouse格式
            List<ClickHouseMetric> clickHouseMetrics = converter.convertToClickHouseFormat(prometheusMetrics);
            log.info("Converted {} metrics to ClickHouse format", clickHouseMetrics.size());

            // 存储到ClickHouse
            if (!clickHouseMetrics.isEmpty()) {
                clickHouseMetricsService.addMetrics(clickHouseMetrics);
                log.info("Successfully added {} metrics to ClickHouse buffer", clickHouseMetrics.size());
            }

            // 记录解析的指标（可选，用于调试）
            if (log.isDebugEnabled()) {
                logParsedMetrics(clickHouseMetrics);
            }
        } catch (Exception e) {
            log.error("Error processing metrics for request: {}", request.getRequestPath(), e);
            throw new RuntimeException("Failed to process metrics", e);
        }
    }

    private void logParsedMetrics(List<ClickHouseMetric> metrics) {
        log.info("Parsed Metrics : {}", JSONUtil.toJsonStr(metrics));
    }
}