package com.example.demo.service;

import com.example.demo.config.ClickHouseProperties;
import com.example.demo.dao.ClickHouseMetricsDao;
import com.example.demo.entity.PrometheusMetricEntity;
import com.example.demo.model.ClickHouseMetric;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * ClickHouse指标存储服务
 * 提供批量插入、查询和统计功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ClickHouseMetricsService {

    private final ClickHouseMetricsDao clickHouseMetricsDao;
    private final MetricsConversionService conversionService;
    private final ClickHouseProperties clickHouseProperties;

    // 批量插入缓冲区
    private final ConcurrentLinkedQueue<ClickHouseMetric> metricsBuffer = new ConcurrentLinkedQueue<>();
    
    // 定时任务执行器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    /**
     * 初始化定时批量插入任务
     */
    public void initBatchInsertTask() {
        // 定时批量插入任务
        scheduler.scheduleAtFixedRate(
            this::flushMetricsBuffer,
            clickHouseProperties.getBatch().getTimeout(),
            clickHouseProperties.getBatch().getTimeout(),
            TimeUnit.MILLISECONDS
        );
        
        log.info("ClickHouse batch insert task initialized with batch size: {}, timeout: {}ms",
            clickHouseProperties.getBatch().getSize(),
            clickHouseProperties.getBatch().getTimeout());
    }

    /**
     * 添加单个指标到缓冲区
     */
    public void addMetric(ClickHouseMetric metric) {
        if (metric == null || !conversionService.isValidMetric(metric)) {
            log.warn("Invalid metric, skipping: {}", metric);
            return;
        }

        metricsBuffer.offer(metric);
        
        // 如果缓冲区达到批量大小，立即刷新
        if (metricsBuffer.size() >= clickHouseProperties.getBatch().getSize()) {
            flushMetricsBuffer();
        }
    }

    /**
     * 批量添加指标到缓冲区
     */
    public void addMetrics(List<ClickHouseMetric> metrics) {
        if (metrics == null || metrics.isEmpty()) {
            return;
        }

        // 过滤有效指标
        List<ClickHouseMetric> validMetrics = conversionService.filterValidMetrics(metrics);
        
        // 添加到缓冲区
        validMetrics.forEach(metricsBuffer::offer);
        
        log.info("Added {} valid metrics to buffer, current buffer size: {}", 
            validMetrics.size(), metricsBuffer.size());

        // 如果缓冲区达到批量大小，立即刷新
        if (metricsBuffer.size() >= clickHouseProperties.getBatch().getSize()) {
            flushMetricsBuffer();
        }
    }

    /**
     * 立即插入指标数据（同步）
     */
    public void insertMetricsImmediately(List<ClickHouseMetric> metrics) {
        if (metrics == null || metrics.isEmpty()) {
            return;
        }

        List<ClickHouseMetric> validMetrics = conversionService.filterValidMetrics(metrics);
        if (validMetrics.isEmpty()) {
            log.warn("No valid metrics to insert");
            return;
        }

        List<PrometheusMetricEntity> entities = conversionService.convertToEntities(validMetrics);
        
        try {
            clickHouseMetricsDao.batchInsertMetrics(entities);
            log.info("Successfully inserted {} metrics immediately", entities.size());
        } catch (Exception e) {
            log.error("Failed to insert metrics immediately", e);
            throw new RuntimeException("Failed to insert metrics", e);
        }
    }

    /**
     * 异步插入指标数据
     */
    @Async
    public CompletableFuture<Void> insertMetricsAsync(List<ClickHouseMetric> metrics) {
        return CompletableFuture.runAsync(() -> {
            try {
                insertMetricsImmediately(metrics);
            } catch (Exception e) {
                log.error("Async metrics insertion failed", e);
                throw new RuntimeException("Async metrics insertion failed", e);
            }
        });
    }

    /**
     * 刷新指标缓冲区
     */
    private synchronized void flushMetricsBuffer() {
        if (metricsBuffer.isEmpty()) {
            return;
        }

        List<ClickHouseMetric> metricsToInsert = new ArrayList<>();
        
        // 从缓冲区取出指标
        ClickHouseMetric metric;
        while ((metric = metricsBuffer.poll()) != null && 
               metricsToInsert.size() < clickHouseProperties.getBatch().getSize()) {
            metricsToInsert.add(metric);
        }

        if (!metricsToInsert.isEmpty()) {
            try {
                insertMetricsImmediately(metricsToInsert);
                log.info("Flushed {} metrics from buffer", metricsToInsert.size());
            } catch (Exception e) {
                log.error("Failed to flush metrics buffer", e);
                // 将失败的指标重新放回缓冲区
                metricsToInsert.forEach(metricsBuffer::offer);
            }
        }
    }

    /**
     * 根据时间范围查询指标数据
     */
    public List<ClickHouseMetric> queryMetricsByTimeRange(LocalDateTime startTime, LocalDateTime endTime,
                                                          String metricName, String job, int limit) {
        try {
            List<PrometheusMetricEntity> entities = clickHouseMetricsDao.findMetricsByTimeRange(
                startTime, endTime, metricName, job, limit);
            
            return conversionService.convertFromEntities(entities);
        } catch (Exception e) {
            log.error("Failed to query metrics by time range", e);
            throw new RuntimeException("Failed to query metrics", e);
        }
    }

    /**
     * 查询指标统计信息
     */
    public List<ClickHouseMetricsDao.MetricStatistics> getMetricStatistics(LocalDateTime startTime, 
                                                                           LocalDateTime endTime, 
                                                                           String metricName) {
        try {
            return clickHouseMetricsDao.getMetricStatistics(startTime, endTime, metricName);
        } catch (Exception e) {
            log.error("Failed to get metric statistics", e);
            throw new RuntimeException("Failed to get metric statistics", e);
        }
    }

    /**
     * 获取缓冲区状态
     */
    public BufferStatus getBufferStatus() {
        return new BufferStatus(
            metricsBuffer.size(),
            clickHouseProperties.getBatch().getSize(),
            clickHouseProperties.getBatch().getTimeout()
        );
    }

    /**
     * 强制刷新缓冲区
     */
    public void forceFlushBuffer() {
        flushMetricsBuffer();
    }

    /**
     * 关闭服务
     */
    public void shutdown() {
        // 刷新剩余数据
        flushMetricsBuffer();
        
        // 关闭定时任务
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        log.info("ClickHouse metrics service shutdown completed");
    }

    /**
     * 缓冲区状态信息
     */
    public static class BufferStatus {
        private final int currentSize;
        private final int batchSize;
        private final long flushInterval;

        public BufferStatus(int currentSize, int batchSize, long flushInterval) {
            this.currentSize = currentSize;
            this.batchSize = batchSize;
            this.flushInterval = flushInterval;
        }

        public int getCurrentSize() { return currentSize; }
        public int getBatchSize() { return batchSize; }
        public long getFlushInterval() { return flushInterval; }
        public double getBufferUsagePercentage() { 
            return batchSize > 0 ? (double) currentSize / batchSize * 100 : 0; 
        }
    }
}
