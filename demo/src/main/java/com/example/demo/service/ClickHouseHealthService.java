package com.example.demo.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * ClickHouse健康检查和监控服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ClickHouseHealthService {

    private final JdbcTemplate clickHouseJdbcTemplate;

    /**
     * 检查ClickHouse连接健康状态
     */
    public HealthStatus checkHealth() {
        HealthStatus status = new HealthStatus();
        
        try {
            // 基本连接测试
            long startTime = System.currentTimeMillis();
            Integer result = clickHouseJdbcTemplate.queryForObject("SELECT 1", Integer.class);
            long responseTime = System.currentTimeMillis() - startTime;
            
            if (result != null && result == 1) {
                status.setHealthy(true);
                status.setResponseTimeMs(responseTime);
                status.setMessage("ClickHouse connection is healthy");
            } else {
                status.setHealthy(false);
                status.setMessage("ClickHouse connection test failed");
            }
            
        } catch (Exception e) {
            status.setHealthy(false);
            status.setMessage("ClickHouse connection failed: " + e.getMessage());
            status.setError(e.getClass().getSimpleName());
            log.error("ClickHouse health check failed", e);
        }
        
        status.setCheckTime(LocalDateTime.now());
        return status;
    }

    /**
     * 获取ClickHouse系统信息
     */
    public SystemInfo getSystemInfo() {
        SystemInfo info = new SystemInfo();
        
        try {
            // 查询系统信息
            Map<String, Object> systemMetrics = new HashMap<>();
            
            // 查询版本信息
            String version = clickHouseJdbcTemplate.queryForObject("SELECT version()", String.class);
            systemMetrics.put("version", version);
            
            // 查询正在运行的查询数量
            Integer runningQueries = clickHouseJdbcTemplate.queryForObject(
                "SELECT count() FROM system.processes", Integer.class);
            systemMetrics.put("running_queries", runningQueries);
            
            // 查询数据库大小
            Long databaseSize = clickHouseJdbcTemplate.queryForObject(
                "SELECT sum(bytes_on_disk) FROM system.parts WHERE database = 'metrics_db'", Long.class);
            systemMetrics.put("database_size_bytes", databaseSize != null ? databaseSize : 0L);
            
            // 查询表行数
            Long tableRows = clickHouseJdbcTemplate.queryForObject(
                "SELECT sum(rows) FROM system.parts WHERE database = 'metrics_db' AND table = 'prometheus_metrics'", 
                Long.class);
            systemMetrics.put("prometheus_metrics_rows", tableRows != null ? tableRows : 0L);
            
            info.setSystemMetrics(systemMetrics);
            info.setHealthy(true);
            
        } catch (Exception e) {
            info.setHealthy(false);
            info.setError("Failed to get system info: " + e.getMessage());
            log.error("Failed to get ClickHouse system info", e);
        }
        
        info.setCheckTime(LocalDateTime.now());
        return info;
    }

    /**
     * 获取表统计信息
     */
    public TableStats getTableStats() {
        TableStats stats = new TableStats();
        
        try {
            // 查询主表统计
            Map<String, Object> mainTableStats = clickHouseJdbcTemplate.queryForMap("""
                SELECT 
                    count() as total_rows,
                    uniq(metric_name) as unique_metrics,
                    uniq(job) as unique_jobs,
                    uniq(app_id) as unique_apps,
                    min(timestamp) as earliest_timestamp,
                    max(timestamp) as latest_timestamp
                FROM prometheus_metrics
                """);
            
            stats.setMainTableStats(mainTableStats);
            
            // 查询最近24小时的数据量
            Long recentRows = clickHouseJdbcTemplate.queryForObject("""
                SELECT count() FROM prometheus_metrics 
                WHERE timestamp >= now() - INTERVAL 24 HOUR
                """, Long.class);
            
            stats.setRecentRows(recentRows != null ? recentRows : 0L);
            stats.setHealthy(true);
            
        } catch (Exception e) {
            stats.setHealthy(false);
            stats.setError("Failed to get table stats: " + e.getMessage());
            log.error("Failed to get table stats", e);
        }
        
        stats.setCheckTime(LocalDateTime.now());
        return stats;
    }

    /**
     * 优化表性能
     */
    public OptimizationResult optimizeTables() {
        OptimizationResult result = new OptimizationResult();
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 优化主表
            clickHouseJdbcTemplate.execute("OPTIMIZE TABLE prometheus_metrics FINAL");
            
            // 优化物化视图
            clickHouseJdbcTemplate.execute("OPTIMIZE TABLE metrics_hourly_stats FINAL");
            clickHouseJdbcTemplate.execute("OPTIMIZE TABLE metrics_daily_stats FINAL");
            
            long duration = System.currentTimeMillis() - startTime;
            
            result.setSuccess(true);
            result.setDurationMs(duration);
            result.setMessage("Tables optimized successfully");
            
            log.info("ClickHouse tables optimized in {}ms", duration);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("Failed to optimize tables: " + e.getMessage());
            log.error("Failed to optimize ClickHouse tables", e);
        }
        
        result.setOptimizationTime(LocalDateTime.now());
        return result;
    }

    /**
     * 健康状态类
     */
    public static class HealthStatus {
        private boolean healthy;
        private long responseTimeMs;
        private String message;
        private String error;
        private LocalDateTime checkTime;

        // Getters and setters
        public boolean isHealthy() { return healthy; }
        public void setHealthy(boolean healthy) { this.healthy = healthy; }
        public long getResponseTimeMs() { return responseTimeMs; }
        public void setResponseTimeMs(long responseTimeMs) { this.responseTimeMs = responseTimeMs; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
        public LocalDateTime getCheckTime() { return checkTime; }
        public void setCheckTime(LocalDateTime checkTime) { this.checkTime = checkTime; }
    }

    /**
     * 系统信息类
     */
    public static class SystemInfo {
        private boolean healthy;
        private Map<String, Object> systemMetrics;
        private String error;
        private LocalDateTime checkTime;

        // Getters and setters
        public boolean isHealthy() { return healthy; }
        public void setHealthy(boolean healthy) { this.healthy = healthy; }
        public Map<String, Object> getSystemMetrics() { return systemMetrics; }
        public void setSystemMetrics(Map<String, Object> systemMetrics) { this.systemMetrics = systemMetrics; }
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
        public LocalDateTime getCheckTime() { return checkTime; }
        public void setCheckTime(LocalDateTime checkTime) { this.checkTime = checkTime; }
    }

    /**
     * 表统计信息类
     */
    public static class TableStats {
        private boolean healthy;
        private Map<String, Object> mainTableStats;
        private long recentRows;
        private String error;
        private LocalDateTime checkTime;

        // Getters and setters
        public boolean isHealthy() { return healthy; }
        public void setHealthy(boolean healthy) { this.healthy = healthy; }
        public Map<String, Object> getMainTableStats() { return mainTableStats; }
        public void setMainTableStats(Map<String, Object> mainTableStats) { this.mainTableStats = mainTableStats; }
        public long getRecentRows() { return recentRows; }
        public void setRecentRows(long recentRows) { this.recentRows = recentRows; }
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
        public LocalDateTime getCheckTime() { return checkTime; }
        public void setCheckTime(LocalDateTime checkTime) { this.checkTime = checkTime; }
    }

    /**
     * 优化结果类
     */
    public static class OptimizationResult {
        private boolean success;
        private long durationMs;
        private String message;
        private LocalDateTime optimizationTime;

        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public long getDurationMs() { return durationMs; }
        public void setDurationMs(long durationMs) { this.durationMs = durationMs; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public LocalDateTime getOptimizationTime() { return optimizationTime; }
        public void setOptimizationTime(LocalDateTime optimizationTime) { this.optimizationTime = optimizationTime; }
    }
}
