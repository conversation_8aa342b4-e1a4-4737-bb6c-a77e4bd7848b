package com.example.demo.service;

import com.example.demo.entity.LabelSet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 标签集合服务
 * 管理标签组合的字典映射
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LabelSetService {

    private final JdbcTemplate clickHouseJdbcTemplate;
    private final LabelDictionaryService labelDictionaryService;
    
    // 标签集合缓存
    private final Map<Long, Long> hashToIdCache = new ConcurrentHashMap<>();
    private final Map<Long, List<LabelSet.LabelPair>> idToLabelsCache = new ConcurrentHashMap<>();

    /**
     * 获取或创建标签集合ID
     */
    public Long getOrCreateLabelSetId(Map<String, String> labels) {
        if (labels == null || labels.isEmpty()) {
            return null;
        }

        // 计算标签组合的哈希值
        Long labelsHash = calculateLabelsHash(labels);
        
        // 先从缓存查找
        Long labelSetId = hashToIdCache.get(labelsHash);
        if (labelSetId != null) {
            return labelSetId;
        }

        // 从数据库查找
        labelSetId = findLabelSetId(labelsHash);
        if (labelSetId != null) {
            hashToIdCache.put(labelsHash, labelSetId);
            return labelSetId;
        }

        // 创建新的标签集合
        labelSetId = createLabelSet(labels, labelsHash);
        hashToIdCache.put(labelsHash, labelSetId);
        
        log.debug("Created new label set: {} -> {}", labels, labelSetId);
        return labelSetId;
    }

    /**
     * 根据标签集合ID获取标签信息
     */
    public Map<String, String> getLabelsBySetId(Long labelSetId) {
        if (labelSetId == null) {
            return Collections.emptyMap();
        }

        // 先从缓存查找
        List<LabelSet.LabelPair> labelPairs = idToLabelsCache.get(labelSetId);
        if (labelPairs == null) {
            // 从数据库查询
            labelPairs = findLabelPairsBySetId(labelSetId);
            if (labelPairs != null) {
                idToLabelsCache.put(labelSetId, labelPairs);
            } else {
                return Collections.emptyMap();
            }
        }

        // 转换为标签映射
        Map<String, String> labels = new HashMap<>();
        for (LabelSet.LabelPair pair : labelPairs) {
            String keyName = labelDictionaryService.getLabelKeyName(pair.getKeyId());
            String valueText = labelDictionaryService.getLabelValueText(pair.getValueId());
            
            if (keyName != null && valueText != null) {
                labels.put(keyName, valueText);
            }
        }

        return labels;
    }

    /**
     * 批量获取标签集合ID
     */
    public Map<Map<String, String>, Long> batchGetLabelSetIds(List<Map<String, String>> labelsList) {
        Map<Map<String, String>, Long> result = new HashMap<>();
        
        for (Map<String, String> labels : labelsList) {
            Long labelSetId = getOrCreateLabelSetId(labels);
            result.put(labels, labelSetId);
        }
        
        return result;
    }

    /**
     * 根据标签过滤条件查找标签集合ID
     */
    public List<Long> findLabelSetIdsByFilter(Map<String, String> filterLabels) {
        if (filterLabels == null || filterLabels.isEmpty()) {
            return Collections.emptyList();
        }

        // 构建查询条件
        StringBuilder sql = new StringBuilder("""
            SELECT DISTINCT ls.id 
            FROM label_sets ls
            WHERE 1=1
            """);

        List<Object> params = new ArrayList<>();
        
        for (Map.Entry<String, String> entry : filterLabels.entrySet()) {
            String keyName = entry.getKey();
            String valueText = entry.getValue();
            
            // 获取键值ID
            Long keyId = labelDictionaryService.getOrCreateLabelKeyId(keyName);
            Long valueId = labelDictionaryService.getOrCreateLabelValueId(keyId, valueText);
            
            sql.append(" AND arrayExists(x -> x.1 = ? AND x.2 = ?, ls.label_pairs)");
            params.add(keyId);
            params.add(valueId);
        }

        try {
            return clickHouseJdbcTemplate.queryForList(sql.toString(), Long.class, params.toArray());
        } catch (Exception e) {
            log.error("Failed to find label set IDs by filter: {}", filterLabels, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取标签集合统计信息
     */
    public LabelSetStats getStats() {
        try {
            // 查询总数
            Long totalSets = clickHouseJdbcTemplate.queryForObject(
                "SELECT count() FROM label_sets", Long.class);
            
            // 查询最近创建的数量
            Long recentSets = clickHouseJdbcTemplate.queryForObject(
                "SELECT count() FROM label_sets WHERE created_at >= now() - INTERVAL 1 DAY", 
                Long.class);
            
            return LabelSetStats.builder()
                .totalSets(totalSets != null ? totalSets : 0L)
                .recentSets(recentSets != null ? recentSets : 0L)
                .cacheSize(hashToIdCache.size())
                .build();
                
        } catch (Exception e) {
            log.error("Failed to get label set stats", e);
            return LabelSetStats.builder().build();
        }
    }

    /**
     * 清理缓存
     */
    public void clearCache() {
        hashToIdCache.clear();
        idToLabelsCache.clear();
        log.info("Label set cache cleared");
    }

    /**
     * 预加载热门标签集合到缓存
     */
    public void preloadCache(int limit) {
        try {
            List<LabelSet> labelSets = clickHouseJdbcTemplate.query(
                "SELECT id, label_pairs, labels_hash, created_at FROM label_sets ORDER BY created_at DESC LIMIT ?",
                new LabelSetRowMapper(),
                limit
            );
            
            for (LabelSet labelSet : labelSets) {
                hashToIdCache.put(labelSet.getLabelsHash(), labelSet.getId());
                idToLabelsCache.put(labelSet.getId(), labelSet.getLabelPairs());
            }
            
            log.info("Preloaded {} label sets to cache", labelSets.size());
            
        } catch (Exception e) {
            log.error("Failed to preload label set cache", e);
        }
    }

    // 私有方法

    /**
     * 计算标签组合的哈希值
     */
    private Long calculateLabelsHash(Map<String, String> labels) {
        // 将标签按键排序，确保相同标签组合产生相同哈希
        String sortedLabels = labels.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .map(entry -> entry.getKey() + "=" + entry.getValue())
            .collect(Collectors.joining(","));
        
        return (long) sortedLabels.hashCode();
    }

    private Long findLabelSetId(Long labelsHash) {
        try {
            return clickHouseJdbcTemplate.queryForObject(
                "SELECT id FROM label_sets WHERE labels_hash = ? LIMIT 1",
                Long.class,
                labelsHash
            );
        } catch (Exception e) {
            return null;
        }
    }

    private Long createLabelSet(Map<String, String> labels, Long labelsHash) {
        // 生成新ID
        Long newId = System.currentTimeMillis() % 1000000000L;
        
        // 转换标签为键值对数组
        List<LabelSet.LabelPair> labelPairs = new ArrayList<>();
        for (Map.Entry<String, String> entry : labels.entrySet()) {
            Long keyId = labelDictionaryService.getOrCreateLabelKeyId(entry.getKey());
            Long valueId = labelDictionaryService.getOrCreateLabelValueId(keyId, entry.getValue());
            
            labelPairs.add(LabelSet.LabelPair.builder()
                .keyId(keyId)
                .valueId(valueId)
                .build());
        }

        // 构建SQL插入语句
        StringBuilder pairsArray = new StringBuilder("[");
        for (int i = 0; i < labelPairs.size(); i++) {
            if (i > 0) pairsArray.append(", ");
            LabelSet.LabelPair pair = labelPairs.get(i);
            pairsArray.append("(").append(pair.getKeyId()).append(", ").append(pair.getValueId()).append(")");
        }
        pairsArray.append("]");

        clickHouseJdbcTemplate.update(
            "INSERT INTO label_sets (id, label_pairs, labels_hash, created_at) VALUES (?, " + pairsArray + ", ?, ?)",
            newId, labelsHash, LocalDateTime.now()
        );
        
        // 缓存标签对信息
        idToLabelsCache.put(newId, labelPairs);
        
        return newId;
    }

    private List<LabelSet.LabelPair> findLabelPairsBySetId(Long labelSetId) {
        try {
            LabelSet labelSet = clickHouseJdbcTemplate.queryForObject(
                "SELECT id, label_pairs, labels_hash, created_at FROM label_sets WHERE id = ?",
                new LabelSetRowMapper(),
                labelSetId
            );
            
            return labelSet != null ? labelSet.getLabelPairs() : null;
        } catch (Exception e) {
            log.warn("Failed to find label pairs for set id: {}", labelSetId);
            return null;
        }
    }

    // 行映射器
    private static class LabelSetRowMapper implements RowMapper<LabelSet> {
        @Override
        public LabelSet mapRow(ResultSet rs, int rowNum) throws SQLException {
            // 这里需要解析ClickHouse的Array(Tuple(UInt32, UInt64))格式
            // 简化实现，实际需要根据ClickHouse JDBC驱动的具体格式来解析
            String labelPairsStr = rs.getString("label_pairs");
            List<LabelSet.LabelPair> labelPairs = parseLabelPairs(labelPairsStr);
            
            return LabelSet.builder()
                .id(rs.getLong("id"))
                .labelPairs(labelPairs)
                .labelsHash(rs.getLong("labels_hash"))
                .createdAt(rs.getTimestamp("created_at").toLocalDateTime())
                .build();
        }
        
        private List<LabelSet.LabelPair> parseLabelPairs(String labelPairsStr) {
            // 简化的解析实现，实际需要更复杂的解析逻辑
            List<LabelSet.LabelPair> pairs = new ArrayList<>();
            // TODO: 实现具体的解析逻辑
            return pairs;
        }
    }

    // 统计信息
    @lombok.Data
    @lombok.Builder
    public static class LabelSetStats {
        private long totalSets;
        private long recentSets;
        private int cacheSize;
    }
}
