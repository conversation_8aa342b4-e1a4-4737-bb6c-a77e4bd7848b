package com.example.demo.service;

import com.example.demo.entity.LabelKey;
import com.example.demo.entity.LabelValue;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 标签字典服务
 * 管理标签键和标签值的字典映射
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LabelDictionaryService {

    private final JdbcTemplate clickHouseJdbcTemplate;
    
    // 内存缓存，提升查询性能
    private final Map<String, Long> keyNameToIdCache = new ConcurrentHashMap<>();
    private final Map<Long, String> keyIdToNameCache = new ConcurrentHashMap<>();
    private final Map<String, Long> valueToIdCache = new ConcurrentHashMap<>();
    private final Map<Long, String> valueIdToTextCache = new ConcurrentHashMap<>();

    /**
     * 获取或创建标签键ID
     */
    public Long getOrCreateLabelKeyId(String keyName) {
        // 先从缓存查找
        Long keyId = keyNameToIdCache.get(keyName);
        if (keyId != null) {
            return keyId;
        }

        // 从数据库查找
        keyId = findLabelKeyId(keyName);
        if (keyId != null) {
            keyNameToIdCache.put(keyName, keyId);
            keyIdToNameCache.put(keyId, keyName);
            return keyId;
        }

        // 创建新的标签键
        keyId = createLabelKey(keyName);
        keyNameToIdCache.put(keyName, keyId);
        keyIdToNameCache.put(keyId, keyName);
        
        log.debug("Created new label key: {} -> {}", keyName, keyId);
        return keyId;
    }

    /**
     * 获取或创建标签值ID
     */
    public Long getOrCreateLabelValueId(Long keyId, String valueText) {
        String cacheKey = keyId + ":" + valueText;
        
        // 先从缓存查找
        Long valueId = valueToIdCache.get(cacheKey);
        if (valueId != null) {
            return valueId;
        }

        // 从数据库查找
        valueId = findLabelValueId(keyId, valueText);
        if (valueId != null) {
            valueToIdCache.put(cacheKey, valueId);
            valueIdToTextCache.put(valueId, valueText);
            return valueId;
        }

        // 创建新的标签值
        valueId = createLabelValue(keyId, valueText);
        valueToIdCache.put(cacheKey, valueId);
        valueIdToTextCache.put(valueId, valueText);
        
        log.debug("Created new label value: {}:{} -> {}", keyId, valueText, valueId);
        return valueId;
    }

    /**
     * 批量获取标签键值对ID
     */
    public Map<String, Long> batchGetLabelKeyIds(List<String> keyNames) {
        Map<String, Long> result = new ConcurrentHashMap<>();
        
        for (String keyName : keyNames) {
            result.put(keyName, getOrCreateLabelKeyId(keyName));
        }
        
        return result;
    }

    /**
     * 根据标签键值对获取ID映射
     */
    public Map<String, Long> getLabelValueIds(Map<String, String> labels) {
        Map<String, Long> result = new ConcurrentHashMap<>();
        
        for (Map.Entry<String, String> entry : labels.entrySet()) {
            String keyName = entry.getKey();
            String valueText = entry.getValue();
            
            Long keyId = getOrCreateLabelKeyId(keyName);
            Long valueId = getOrCreateLabelValueId(keyId, valueText);
            
            result.put(keyName + ":" + valueText, valueId);
        }
        
        return result;
    }

    /**
     * 根据ID获取标签键名称
     */
    public String getLabelKeyName(Long keyId) {
        String keyName = keyIdToNameCache.get(keyId);
        if (keyName != null) {
            return keyName;
        }

        // 从数据库查询
        try {
            keyName = clickHouseJdbcTemplate.queryForObject(
                "SELECT key_name FROM label_keys WHERE id = ?",
                String.class,
                keyId
            );
            
            if (keyName != null) {
                keyIdToNameCache.put(keyId, keyName);
                keyNameToIdCache.put(keyName, keyId);
            }
            
            return keyName;
        } catch (Exception e) {
            log.warn("Failed to find label key for id: {}", keyId);
            return null;
        }
    }

    /**
     * 根据ID获取标签值文本
     */
    public String getLabelValueText(Long valueId) {
        String valueText = valueIdToTextCache.get(valueId);
        if (valueText != null) {
            return valueText;
        }

        // 从数据库查询
        try {
            valueText = clickHouseJdbcTemplate.queryForObject(
                "SELECT value_text FROM label_values WHERE id = ?",
                String.class,
                valueId
            );
            
            if (valueText != null) {
                valueIdToTextCache.put(valueId, valueText);
            }
            
            return valueText;
        } catch (Exception e) {
            log.warn("Failed to find label value for id: {}", valueId);
            return null;
        }
    }

    /**
     * 预加载常用标签到缓存
     */
    public void preloadCache() {
        try {
            // 加载所有标签键
            List<LabelKey> labelKeys = clickHouseJdbcTemplate.query(
                "SELECT id, key_name, created_at FROM label_keys",
                new LabelKeyRowMapper()
            );
            
            for (LabelKey labelKey : labelKeys) {
                keyNameToIdCache.put(labelKey.getKeyName(), labelKey.getId());
                keyIdToNameCache.put(labelKey.getId(), labelKey.getKeyName());
            }

            // 加载热门标签值（可以根据使用频率限制）
            List<LabelValue> labelValues = clickHouseJdbcTemplate.query(
                "SELECT id, key_id, value_text, created_at FROM label_values LIMIT 10000",
                new LabelValueRowMapper()
            );
            
            for (LabelValue labelValue : labelValues) {
                String cacheKey = labelValue.getKeyId() + ":" + labelValue.getValueText();
                valueToIdCache.put(cacheKey, labelValue.getId());
                valueIdToTextCache.put(labelValue.getId(), labelValue.getValueText());
            }
            
            log.info("Preloaded {} label keys and {} label values to cache", 
                labelKeys.size(), labelValues.size());
                
        } catch (Exception e) {
            log.error("Failed to preload label cache", e);
        }
    }

    /**
     * 清理缓存
     */
    public void clearCache() {
        keyNameToIdCache.clear();
        keyIdToNameCache.clear();
        valueToIdCache.clear();
        valueIdToTextCache.clear();
        log.info("Label dictionary cache cleared");
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats() {
        return CacheStats.builder()
            .keyCount(keyNameToIdCache.size())
            .valueCount(valueToIdCache.size())
            .build();
    }

    // 私有方法

    private Long findLabelKeyId(String keyName) {
        try {
            return clickHouseJdbcTemplate.queryForObject(
                "SELECT id FROM label_keys WHERE key_name = ?",
                Long.class,
                keyName
            );
        } catch (Exception e) {
            return null;
        }
    }

    private Long createLabelKey(String keyName) {
        // 生成新ID（简化实现，生产环境可能需要更复杂的ID生成策略）
        Long newId = System.currentTimeMillis() % 1000000000L;
        
        clickHouseJdbcTemplate.update(
            "INSERT INTO label_keys (id, key_name, created_at) VALUES (?, ?, ?)",
            newId, keyName, LocalDateTime.now()
        );
        
        return newId;
    }

    private Long findLabelValueId(Long keyId, String valueText) {
        try {
            return clickHouseJdbcTemplate.queryForObject(
                "SELECT id FROM label_values WHERE key_id = ? AND value_text = ?",
                Long.class,
                keyId, valueText
            );
        } catch (Exception e) {
            return null;
        }
    }

    private Long createLabelValue(Long keyId, String valueText) {
        // 生成新ID
        Long newId = System.currentTimeMillis() % 1000000000L + keyId * 1000000L;
        
        clickHouseJdbcTemplate.update(
            "INSERT INTO label_values (id, key_id, value_text, created_at) VALUES (?, ?, ?, ?)",
            newId, keyId, valueText, LocalDateTime.now()
        );
        
        return newId;
    }

    // 行映射器
    private static class LabelKeyRowMapper implements RowMapper<LabelKey> {
        @Override
        public LabelKey mapRow(ResultSet rs, int rowNum) throws SQLException {
            return LabelKey.builder()
                .id(rs.getLong("id"))
                .keyName(rs.getString("key_name"))
                .createdAt(rs.getTimestamp("created_at").toLocalDateTime())
                .build();
        }
    }

    private static class LabelValueRowMapper implements RowMapper<LabelValue> {
        @Override
        public LabelValue mapRow(ResultSet rs, int rowNum) throws SQLException {
            return LabelValue.builder()
                .id(rs.getLong("id"))
                .keyId(rs.getLong("key_id"))
                .valueText(rs.getString("value_text"))
                .createdAt(rs.getTimestamp("created_at").toLocalDateTime())
                .build();
        }
    }

    // 缓存统计信息
    @Data
    @lombok.Builder
    public static class CacheStats {
        private int keyCount;
        private int valueCount;
    }
}
