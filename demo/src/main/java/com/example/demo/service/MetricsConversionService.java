package com.example.demo.service;

import com.example.demo.entity.PrometheusMetricEntity;
import com.example.demo.model.ClickHouseMetric;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 指标数据转换服务
 * 负责在不同数据模型之间进行转换
 */
@Slf4j
@Service
public class MetricsConversionService {

    /**
     * 将ClickHouseMetric转换为PrometheusMetricEntity
     */
    public PrometheusMetricEntity convertToEntity(ClickHouseMetric clickHouseMetric) {
        if (clickHouseMetric == null) {
            return null;
        }

        return PrometheusMetricEntity.builder()
            .timestamp(clickHouseMetric.getTimestamp() != null ? 
                LocalDateTime.ofInstant(clickHouseMetric.getTimestamp(), ZoneId.systemDefault()) : 
                LocalDateTime.now())
            .job(clickHouseMetric.getJob())
            .jobType(clickHouseMetric.getJobType())
            .appId(clickHouseMetric.getAppId())
            .name(clickHouseMetric.getName())
            .instance(clickHouseMetric.getInstance())
            .metricName(clickHouseMetric.getMetricName())
            .metricType(clickHouseMetric.getMetricType())
            .description(clickHouseMetric.getDescription())
            .labelsJson(clickHouseMetric.getLabelsJson())
            .value(clickHouseMetric.getValue())
            .clusterId(clickHouseMetric.getClusterId())
            .deviceType(clickHouseMetric.getDeviceType())
            .deviceId(clickHouseMetric.getDeviceId())
            .ip(clickHouseMetric.getIp())
            .port(parsePort(clickHouseMetric.getPort()))
            .connectPort(parsePort(clickHouseMetric.getConnectPort()))
            .createdAt(LocalDateTime.now())
            .build();
    }

    /**
     * 批量转换ClickHouseMetric列表为PrometheusMetricEntity列表
     */
    public List<PrometheusMetricEntity> convertToEntities(List<ClickHouseMetric> clickHouseMetrics) {
        if (clickHouseMetrics == null || clickHouseMetrics.isEmpty()) {
            return List.of();
        }

        return clickHouseMetrics.stream()
            .map(this::convertToEntity)
            .filter(entity -> entity != null)
            .collect(Collectors.toList());
    }

    /**
     * 将PrometheusMetricEntity转换为ClickHouseMetric
     */
    public ClickHouseMetric convertFromEntity(PrometheusMetricEntity entity) {
        if (entity == null) {
            return null;
        }

        return ClickHouseMetric.builder()
            .timestamp(entity.getTimestamp() != null ? 
                entity.getTimestamp().atZone(ZoneId.systemDefault()).toInstant() : 
                null)
            .job(entity.getJob())
            .jobType(entity.getJobType())
            .appId(entity.getAppId())
            .name(entity.getName())
            .instance(entity.getInstance())
            .metricName(entity.getMetricName())
            .metricType(entity.getMetricType())
            .description(entity.getDescription())
            .labelsJson(entity.getLabelsJson())
            .value(entity.getValue())
            .clusterId(entity.getClusterId())
            .deviceType(entity.getDeviceType())
            .deviceId(entity.getDeviceId())
            .ip(entity.getIp())
            .port(entity.getPort() != null ? entity.getPort().toString() : null)
            .connectPort(entity.getConnectPort() != null ? entity.getConnectPort().toString() : null)
            .build();
    }

    /**
     * 批量转换PrometheusMetricEntity列表为ClickHouseMetric列表
     */
    public List<ClickHouseMetric> convertFromEntities(List<PrometheusMetricEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return List.of();
        }

        return entities.stream()
            .map(this::convertFromEntity)
            .filter(metric -> metric != null)
            .collect(Collectors.toList());
    }

    /**
     * 解析端口字符串为整数
     */
    private Integer parsePort(String portStr) {
        if (portStr == null || portStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            return Integer.parseInt(portStr.trim());
        } catch (NumberFormatException e) {
            log.warn("Invalid port format: {}", portStr);
            return null;
        }
    }

    /**
     * 验证指标数据的有效性
     */
    public boolean isValidMetric(ClickHouseMetric metric) {
        if (metric == null) {
            return false;
        }

        // 必填字段验证
        if (metric.getMetricName() == null || metric.getMetricName().trim().isEmpty()) {
            log.warn("Metric name is required");
            return false;
        }

        if (metric.getValue() == null) {
            log.warn("Metric value is required");
            return false;
        }

        if (metric.getTimestamp() == null) {
            log.warn("Metric timestamp is required");
            return false;
        }

        return true;
    }

    /**
     * 过滤有效的指标数据
     */
    public List<ClickHouseMetric> filterValidMetrics(List<ClickHouseMetric> metrics) {
        if (metrics == null || metrics.isEmpty()) {
            return List.of();
        }

        List<ClickHouseMetric> validMetrics = metrics.stream()
            .filter(this::isValidMetric)
            .collect(Collectors.toList());

        int invalidCount = metrics.size() - validMetrics.size();
        if (invalidCount > 0) {
            log.warn("Filtered out {} invalid metrics", invalidCount);
        }

        return validMetrics;
    }
}
