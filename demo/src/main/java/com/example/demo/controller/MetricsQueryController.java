package com.example.demo.controller;

import com.example.demo.dao.ClickHouseMetricsDao;
import com.example.demo.dto.MetricsQueryRequest;
import com.example.demo.dto.MetricsQueryResponse;
import com.example.demo.model.ClickHouseMetric;
import com.example.demo.service.ClickHouseMetricsService;
import com.example.demo.service.MetricsQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 指标查询控制器
 * 提供各种指标查询和统计API
 */
@Slf4j
@RestController
@RequestMapping("/api/metrics")
@RequiredArgsConstructor
@Validated
public class MetricsQueryController {

    private final MetricsQueryService metricsQueryService;
    private final ClickHouseMetricsService clickHouseMetricsService;

    /**
     * 查询指标数据
     */
    @GetMapping("/query")
    public ResponseEntity<MetricsQueryResponse> queryMetrics(@Valid @ModelAttribute MetricsQueryRequest request) {
        try {
            log.info("Querying metrics with request: {}", request);
            MetricsQueryResponse response = metricsQueryService.queryMetrics(request);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid query request: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Failed to query metrics", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查询最近的指标数据
     */
    @GetMapping("/recent")
    public ResponseEntity<List<ClickHouseMetric>> queryRecentMetrics(
            @RequestParam(required = false) String metricName,
            @RequestParam(required = false) String job,
            @RequestParam(defaultValue = "1") @Min(1) @Max(168) int hours,
            @RequestParam(defaultValue = "100") @Min(1) @Max(10000) int limit) {
        
        try {
            List<ClickHouseMetric> metrics = metricsQueryService.queryRecentMetrics(metricName, job, hours, limit);
            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            log.error("Failed to query recent metrics", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查询指标统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<List<ClickHouseMetricsDao.MetricStatistics>> queryStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) String metricName) {
        
        try {
            List<ClickHouseMetricsDao.MetricStatistics> statistics = 
                metricsQueryService.queryMetricStatistics(startTime, endTime, metricName);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("Failed to query statistics", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查询热门指标
     */
    @GetMapping("/top")
    public ResponseEntity<List<ClickHouseMetricsDao.MetricStatistics>> queryTopMetrics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(defaultValue = "10") @Min(1) @Max(100) int limit) {
        
        try {
            List<ClickHouseMetricsDao.MetricStatistics> topMetrics = 
                metricsQueryService.queryTopMetrics(startTime, endTime, limit);
            return ResponseEntity.ok(topMetrics);
        } catch (Exception e) {
            log.error("Failed to query top metrics", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查询指标趋势
     */
    @GetMapping("/trends")
    public ResponseEntity<List<MetricsQueryService.MetricTrend>> queryTrends(
            @RequestParam String metricName,
            @RequestParam(required = false) String job,
            @RequestParam(defaultValue = "7") @Min(1) @Max(30) int days) {
        
        try {
            List<MetricsQueryService.MetricTrend> trends = 
                metricsQueryService.queryMetricTrends(metricName, job, days);
            return ResponseEntity.ok(trends);
        } catch (Exception e) {
            log.error("Failed to query metric trends", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取缓冲区状态
     */
    @GetMapping("/buffer/status")
    public ResponseEntity<ClickHouseMetricsService.BufferStatus> getBufferStatus() {
        try {
            ClickHouseMetricsService.BufferStatus status = clickHouseMetricsService.getBufferStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("Failed to get buffer status", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 强制刷新缓冲区
     */
    @PostMapping("/buffer/flush")
    public ResponseEntity<String> flushBuffer() {
        try {
            clickHouseMetricsService.forceFlushBuffer();
            return ResponseEntity.ok("Buffer flushed successfully");
        } catch (Exception e) {
            log.error("Failed to flush buffer", e);
            return ResponseEntity.internalServerError().body("Failed to flush buffer");
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        try {
            // 简单的健康检查，可以扩展为更复杂的检查
            ClickHouseMetricsService.BufferStatus status = clickHouseMetricsService.getBufferStatus();
            return ResponseEntity.ok("ClickHouse metrics service is healthy. Buffer size: " + status.getCurrentSize());
        } catch (Exception e) {
            log.error("Health check failed", e);
            return ResponseEntity.internalServerError().body("Service unhealthy");
        }
    }
}
