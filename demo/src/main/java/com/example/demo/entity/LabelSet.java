package com.example.demo.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 标签集合实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelSet {
    
    /**
     * 标签集合ID
     */
    private Long id;
    
    /**
     * 标签键值对列表 (keyId, valueId)
     */
    private List<LabelPair> labelPairs;
    
    /**
     * 标签组合的哈希值
     */
    private Long labelsHash;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 标签键值对
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LabelPair {
        private Long keyId;
        private Long valueId;
    }
}
