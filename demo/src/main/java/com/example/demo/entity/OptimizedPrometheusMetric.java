package com.example.demo.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优化后的Prometheus指标实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OptimizedPrometheusMetric {

    /**
     * 时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 任务名称（保留常用字段以提升查询性能）
     */
    private String job;

    /**
     * 任务类型
     */
    private String jobType;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 实例
     */
    private String instance;

    /**
     * 指标名称
     */
    private String metricName;

    /**
     * 指标类型
     */
    private String metricType;

    /**
     * 指标值
     */
    private BigDecimal value;

    /**
     * 标签集合ID（引用label_sets表）
     */
    private Long labelSetId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
