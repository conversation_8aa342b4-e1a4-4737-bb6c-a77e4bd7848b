package com.example.demo.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 标签值实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelValue {
    
    /**
     * 标签值ID
     */
    private Long id;
    
    /**
     * 标签键ID
     */
    private Long keyId;
    
    /**
     * 标签值文本
     */
    private String valueText;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
