package com.example.demo.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ClickHouse中prometheus_metrics表对应的实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrometheusMetricEntity {

    /**
     * 时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 任务名称
     */
    private String job;

    /**
     * 任务类型
     */
    private String jobType;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 名称
     */
    private String name;

    /**
     * 实例
     */
    private String instance;

    /**
     * 指标名称
     */
    private String metricName;

    /**
     * 指标类型
     */
    private String metricType;

    /**
     * 描述
     */
    private String description;

    /**
     * 标签JSON
     */
    private String labelsJson;

    /**
     * 指标值
     */
    private BigDecimal value;

    /**
     * 集群ID
     */
    private String clusterId;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 连接端口
     */
    private Integer connectPort;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
