package com.example.demo.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 标签键实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelKey {
    
    /**
     * 标签键ID
     */
    private Long id;
    
    /**
     * 标签键名称
     */
    private String keyName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
