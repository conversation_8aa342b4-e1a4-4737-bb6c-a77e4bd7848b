package com.example.demo.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * ClickHouse数据库配置类
 * 配置连接池和JdbcTemplate
 */
@Slf4j
@Configuration
public class ClickHouseConfig {

    @Value("${spring.datasource.clickhouse.url}")
    private String url;

    @Value("${spring.datasource.clickhouse.username}")
    private String username;

    @Value("${spring.datasource.clickhouse.password}")
    private String password;

    @Value("${spring.datasource.clickhouse.driver-class-name}")
    private String driverClassName;

    @Value("${spring.datasource.clickhouse.hikari.maximum-pool-size:20}")
    private int maximumPoolSize;

    @Value("${spring.datasource.clickhouse.hikari.minimum-idle:5}")
    private int minimumIdle;

    @Value("${spring.datasource.clickhouse.hikari.idle-timeout:300000}")
    private long idleTimeout;

    @Value("${spring.datasource.clickhouse.hikari.max-lifetime:1800000}")
    private long maxLifetime;

    @Value("${spring.datasource.clickhouse.hikari.connection-timeout:30000}")
    private long connectionTimeout;

    @Value("${spring.datasource.clickhouse.hikari.validation-timeout:5000}")
    private long validationTimeout;

    @Value("${spring.datasource.clickhouse.hikari.leak-detection-threshold:60000}")
    private long leakDetectionThreshold;

    /**
     * 配置ClickHouse数据源
     */
    @Bean(name = "clickHouseDataSource")
    @Primary
    public DataSource clickHouseDataSource() {
        HikariConfig config = new HikariConfig();
        
        // 基本连接配置
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName(driverClassName);
        
        // 连接池配置
        config.setMaximumPoolSize(maximumPoolSize);
        config.setMinimumIdle(minimumIdle);
        config.setIdleTimeout(idleTimeout);
        config.setMaxLifetime(maxLifetime);
        config.setConnectionTimeout(connectionTimeout);
        config.setValidationTimeout(validationTimeout);
        config.setLeakDetectionThreshold(leakDetectionThreshold);
        
        // ClickHouse特定配置
        config.addDataSourceProperty("socket_timeout", "30000");
        config.addDataSourceProperty("connection_timeout", "10000");
        config.addDataSourceProperty("compress", "true");
        config.addDataSourceProperty("decompress", "true");
        config.addDataSourceProperty("use_server_time_zone", "true");
        config.addDataSourceProperty("use_time_zone", "Asia/Shanghai");
        
        // 连接池名称
        config.setPoolName("ClickHousePool");
        
        // 连接测试查询
        config.setConnectionTestQuery("SELECT 1");
        
        log.info("Initializing ClickHouse DataSource with URL: {}", url);
        
        return new HikariDataSource(config);
    }

    /**
     * 配置ClickHouse JdbcTemplate
     */
    @Bean(name = "clickHouseJdbcTemplate")
    @Primary
    public JdbcTemplate clickHouseJdbcTemplate() {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(clickHouseDataSource());
        
        // 设置查询超时时间（秒）
        jdbcTemplate.setQueryTimeout(30);
        
        // 设置最大行数限制
        jdbcTemplate.setMaxRows(100000);
        
        // 设置获取大小
        jdbcTemplate.setFetchSize(1000);
        
        log.info("ClickHouse JdbcTemplate configured successfully");
        
        return jdbcTemplate;
    }
}
