package com.example.demo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * ClickHouse配置属性类
 */
@Data
@Component
@ConfigurationProperties(prefix = "clickhouse")
public class ClickHouseProperties {

    /**
     * 批量插入配置
     */
    private Batch batch = new Batch();

    /**
     * 查询配置
     */
    private Query query = new Query();

    /**
     * 连接配置
     */
    private Connection connection = new Connection();

    @Data
    public static class Batch {
        /**
         * 批量插入大小
         */
        private int size = 1000;

        /**
         * 批量插入超时时间（毫秒）
         */
        private long timeout = 5000;

        /**
         * 最大等待时间（毫秒）
         */
        private long maxWaitTime = 10000;
    }

    @Data
    public static class Query {
        /**
         * 查询超时时间（毫秒）
         */
        private long timeout = 30000;

        /**
         * 最大返回行数
         */
        private int maxRows = 100000;
    }

    @Data
    public static class Connection {
        /**
         * Socket超时时间（毫秒）
         */
        private long socketTimeout = 30000;

        /**
         * 连接超时时间（毫秒）
         */
        private long connectionTimeout = 10000;

        /**
         * 最大重试次数
         */
        private int maxRetries = 3;
    }
}
