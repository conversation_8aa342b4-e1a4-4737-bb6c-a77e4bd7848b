package com.example.demo.config;

import com.example.demo.service.ClickHouseMetricsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * ClickHouse初始化配置
 * 应用启动时执行初始化任务
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class ClickHouseInitializer implements ApplicationRunner {

    private final ClickHouseMetricsService clickHouseMetricsService;
    private final JdbcTemplate clickHouseJdbcTemplate;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("Initializing ClickHouse services...");
        
        try {
            // 检查ClickHouse连接
            checkClickHouseConnection();
            
            // 初始化批量插入任务
            clickHouseMetricsService.initBatchInsertTask();
            
            // 注册关闭钩子
            registerShutdownHook();
            
            log.info("ClickHouse services initialized successfully");
            
        } catch (Exception e) {
            log.error("Failed to initialize ClickHouse services", e);
            throw e;
        }
    }

    /**
     * 检查ClickHouse连接
     */
    private void checkClickHouseConnection() {
        try {
            Integer result = clickHouseJdbcTemplate.queryForObject("SELECT 1", Integer.class);
            if (result != null && result == 1) {
                log.info("ClickHouse connection test successful");
            } else {
                throw new RuntimeException("ClickHouse connection test failed");
            }
        } catch (Exception e) {
            log.error("ClickHouse connection test failed", e);
            throw new RuntimeException("Failed to connect to ClickHouse", e);
        }
    }

    /**
     * 注册应用关闭钩子
     */
    private void registerShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("Application shutting down, cleaning up ClickHouse services...");
            try {
                clickHouseMetricsService.shutdown();
                log.info("ClickHouse services shutdown completed");
            } catch (Exception e) {
                log.error("Error during ClickHouse services shutdown", e);
            }
        }));
    }
}
