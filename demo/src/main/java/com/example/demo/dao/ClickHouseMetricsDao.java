package com.example.demo.dao;

import com.example.demo.entity.PrometheusMetricEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ClickHouse指标数据访问层
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ClickHouseMetricsDao {

    private final JdbcTemplate clickHouseJdbcTemplate;

    /**
     * 批量插入指标数据
     */
    public void batchInsertMetrics(List<PrometheusMetricEntity> metrics) {
        if (metrics == null || metrics.isEmpty()) {
            log.warn("No metrics to insert");
            return;
        }

        String sql = """
            INSERT INTO prometheus_metrics (
                timestamp, job, job_type, app_id, name, instance,
                metric_name, metric_type, description, labels_json, value,
                cluster_id, device_type, device_id, ip, port, connect_port, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        try {
            int[] results = clickHouseJdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    PrometheusMetricEntity metric = metrics.get(i);
                    
                    ps.setTimestamp(1, metric.getTimestamp() != null ? 
                        Timestamp.valueOf(metric.getTimestamp()) : Timestamp.valueOf(LocalDateTime.now()));
                    ps.setString(2, metric.getJob());
                    ps.setString(3, metric.getJobType());
                    ps.setString(4, metric.getAppId());
                    ps.setString(5, metric.getName());
                    ps.setString(6, metric.getInstance());
                    ps.setString(7, metric.getMetricName());
                    ps.setString(8, metric.getMetricType());
                    ps.setString(9, metric.getDescription());
                    ps.setString(10, metric.getLabelsJson());
                    ps.setBigDecimal(11, metric.getValue());
                    ps.setString(12, metric.getClusterId());
                    ps.setString(13, metric.getDeviceType());
                    ps.setString(14, metric.getDeviceId());
                    ps.setString(15, metric.getIp());
                    
                    if (metric.getPort() != null) {
                        ps.setInt(16, metric.getPort());
                    } else {
                        ps.setNull(16, java.sql.Types.INTEGER);
                    }
                    
                    if (metric.getConnectPort() != null) {
                        ps.setInt(17, metric.getConnectPort());
                    } else {
                        ps.setNull(17, java.sql.Types.INTEGER);
                    }
                    
                    ps.setTimestamp(18, Timestamp.valueOf(LocalDateTime.now()));
                }

                @Override
                public int getBatchSize() {
                    return metrics.size();
                }
            });

            log.info("Successfully inserted {} metrics into ClickHouse", results.length);
            
        } catch (Exception e) {
            log.error("Failed to batch insert metrics into ClickHouse", e);
            throw new RuntimeException("Failed to insert metrics", e);
        }
    }

    /**
     * 根据时间范围查询指标数据
     */
    public List<PrometheusMetricEntity> findMetricsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, 
                                                               String metricName, String job, int limit) {
        StringBuilder sql = new StringBuilder("""
            SELECT timestamp, job, job_type, app_id, name, instance,
                   metric_name, metric_type, description, labels_json, value,
                   cluster_id, device_type, device_id, ip, port, connect_port, created_at
            FROM prometheus_metrics
            WHERE timestamp >= ? AND timestamp <= ?
            """);

        if (metricName != null && !metricName.trim().isEmpty()) {
            sql.append(" AND metric_name = ?");
        }
        
        if (job != null && !job.trim().isEmpty()) {
            sql.append(" AND job = ?");
        }
        
        sql.append(" ORDER BY timestamp DESC");
        
        if (limit > 0) {
            sql.append(" LIMIT ?");
        }

        try {
            return clickHouseJdbcTemplate.query(sql.toString(), 
                new Object[]{
                    Timestamp.valueOf(startTime),
                    Timestamp.valueOf(endTime),
                    metricName,
                    job,
                    limit
                }, 
                new PrometheusMetricRowMapper());
                
        } catch (Exception e) {
            log.error("Failed to query metrics from ClickHouse", e);
            throw new RuntimeException("Failed to query metrics", e);
        }
    }

    /**
     * 查询指标统计信息
     */
    public List<MetricStatistics> getMetricStatistics(LocalDateTime startTime, LocalDateTime endTime, String metricName) {
        String sql = """
            SELECT 
                metric_name,
                job,
                app_id,
                count() as count,
                avg(value) as avg_value,
                min(value) as min_value,
                max(value) as max_value,
                sum(value) as sum_value
            FROM prometheus_metrics
            WHERE timestamp >= ? AND timestamp <= ?
            """;

        if (metricName != null && !metricName.trim().isEmpty()) {
            sql += " AND metric_name = ?";
        }
        
        sql += " GROUP BY metric_name, job, app_id ORDER BY count DESC";

        try {
            if (metricName != null && !metricName.trim().isEmpty()) {
                return clickHouseJdbcTemplate.query(sql, 
                    new Object[]{Timestamp.valueOf(startTime), Timestamp.valueOf(endTime), metricName},
                    new MetricStatisticsRowMapper());
            } else {
                return clickHouseJdbcTemplate.query(sql, 
                    new Object[]{Timestamp.valueOf(startTime), Timestamp.valueOf(endTime)},
                    new MetricStatisticsRowMapper());
            }
        } catch (Exception e) {
            log.error("Failed to query metric statistics from ClickHouse", e);
            throw new RuntimeException("Failed to query metric statistics", e);
        }
    }

    /**
     * PrometheusMetricEntity行映射器
     */
    private static class PrometheusMetricRowMapper implements RowMapper<PrometheusMetricEntity> {
        @Override
        public PrometheusMetricEntity mapRow(ResultSet rs, int rowNum) throws SQLException {
            return PrometheusMetricEntity.builder()
                .timestamp(rs.getTimestamp("timestamp").toLocalDateTime())
                .job(rs.getString("job"))
                .jobType(rs.getString("job_type"))
                .appId(rs.getString("app_id"))
                .name(rs.getString("name"))
                .instance(rs.getString("instance"))
                .metricName(rs.getString("metric_name"))
                .metricType(rs.getString("metric_type"))
                .description(rs.getString("description"))
                .labelsJson(rs.getString("labels_json"))
                .value(rs.getBigDecimal("value"))
                .clusterId(rs.getString("cluster_id"))
                .deviceType(rs.getString("device_type"))
                .deviceId(rs.getString("device_id"))
                .ip(rs.getString("ip"))
                .port(rs.getObject("port", Integer.class))
                .connectPort(rs.getObject("connect_port", Integer.class))
                .createdAt(rs.getTimestamp("created_at").toLocalDateTime())
                .build();
        }
    }

    /**
     * 指标统计信息类
     */
    public static class MetricStatistics {
        private String metricName;
        private String job;
        private String appId;
        private long count;
        private double avgValue;
        private double minValue;
        private double maxValue;
        private double sumValue;

        // Getters and setters
        public String getMetricName() { return metricName; }
        public void setMetricName(String metricName) { this.metricName = metricName; }
        public String getJob() { return job; }
        public void setJob(String job) { this.job = job; }
        public String getAppId() { return appId; }
        public void setAppId(String appId) { this.appId = appId; }
        public long getCount() { return count; }
        public void setCount(long count) { this.count = count; }
        public double getAvgValue() { return avgValue; }
        public void setAvgValue(double avgValue) { this.avgValue = avgValue; }
        public double getMinValue() { return minValue; }
        public void setMinValue(double minValue) { this.minValue = minValue; }
        public double getMaxValue() { return maxValue; }
        public void setMaxValue(double maxValue) { this.maxValue = maxValue; }
        public double getSumValue() { return sumValue; }
        public void setSumValue(double sumValue) { this.sumValue = sumValue; }
    }

    /**
     * 指标统计信息行映射器
     */
    private static class MetricStatisticsRowMapper implements RowMapper<MetricStatistics> {
        @Override
        public MetricStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
            MetricStatistics stats = new MetricStatistics();
            stats.setMetricName(rs.getString("metric_name"));
            stats.setJob(rs.getString("job"));
            stats.setAppId(rs.getString("app_id"));
            stats.setCount(rs.getLong("count"));
            stats.setAvgValue(rs.getDouble("avg_value"));
            stats.setMinValue(rs.getDouble("min_value"));
            stats.setMaxValue(rs.getDouble("max_value"));
            stats.setSumValue(rs.getDouble("sum_value"));
            return stats;
        }
    }
}
