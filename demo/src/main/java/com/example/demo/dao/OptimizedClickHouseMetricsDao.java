package com.example.demo.dao;

import com.example.demo.entity.OptimizedPrometheusMetric;
import com.example.demo.service.LabelSetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 优化的ClickHouse指标数据访问层
 * 使用标签字典优化存储和查询性能
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class OptimizedClickHouseMetricsDao {

    private final JdbcTemplate clickHouseJdbcTemplate;
    private final LabelSetService labelSetService;

    /**
     * 批量插入优化后的指标数据
     */
    public void batchInsertOptimizedMetrics(List<OptimizedPrometheusMetric> metrics) {
        if (metrics == null || metrics.isEmpty()) {
            log.warn("No optimized metrics to insert");
            return;
        }

        String sql = """
            INSERT INTO prometheus_metrics_optimized (
                timestamp, job, job_type, app_id, instance,
                metric_name, metric_type, value, label_set_id, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        try {
            int[] results = clickHouseJdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    OptimizedPrometheusMetric metric = metrics.get(i);
                    
                    ps.setTimestamp(1, metric.getTimestamp() != null ? 
                        Timestamp.valueOf(metric.getTimestamp()) : Timestamp.valueOf(LocalDateTime.now()));
                    ps.setString(2, metric.getJob());
                    ps.setString(3, metric.getJobType());
                    ps.setString(4, metric.getAppId());
                    ps.setString(5, metric.getInstance());
                    ps.setString(6, metric.getMetricName());
                    ps.setString(7, metric.getMetricType());
                    ps.setBigDecimal(8, metric.getValue());
                    
                    if (metric.getLabelSetId() != null) {
                        ps.setLong(9, metric.getLabelSetId());
                    } else {
                        ps.setNull(9, java.sql.Types.BIGINT);
                    }
                    
                    ps.setTimestamp(10, Timestamp.valueOf(LocalDateTime.now()));
                }

                @Override
                public int getBatchSize() {
                    return metrics.size();
                }
            });

            log.info("Successfully inserted {} optimized metrics into ClickHouse", results.length);
            
        } catch (Exception e) {
            log.error("Failed to batch insert optimized metrics into ClickHouse", e);
            throw new RuntimeException("Failed to insert optimized metrics", e);
        }
    }

    /**
     * 根据时间范围和标签过滤查询指标数据
     */
    public List<OptimizedPrometheusMetric> findOptimizedMetricsByTimeRangeAndLabels(
            LocalDateTime startTime, LocalDateTime endTime, 
            String metricName, Map<String, String> labelFilters, int limit) {
        
        StringBuilder sql = new StringBuilder("""
            SELECT timestamp, job, job_type, app_id, instance,
                   metric_name, metric_type, value, label_set_id, created_at
            FROM prometheus_metrics_optimized
            WHERE timestamp >= ? AND timestamp <= ?
            """);

        List<Object> params = List.of(Timestamp.valueOf(startTime), Timestamp.valueOf(endTime));

        if (metricName != null && !metricName.trim().isEmpty()) {
            sql.append(" AND metric_name = ?");
            params.add(metricName);
        }

        // 如果有标签过滤条件，先查找匹配的标签集合ID
        if (labelFilters != null && !labelFilters.isEmpty()) {
            List<Long> labelSetIds = labelSetService.findLabelSetIdsByFilter(labelFilters);
            
            if (labelSetIds.isEmpty()) {
                // 没有匹配的标签集合，返回空结果
                return List.of();
            }
            
            sql.append(" AND label_set_id IN (");
            for (int i = 0; i < labelSetIds.size(); i++) {
                if (i > 0) sql.append(", ");
                sql.append("?");
                params.add(labelSetIds.get(i));
            }
            sql.append(")");
        }
        
        sql.append(" ORDER BY timestamp DESC");
        
        if (limit > 0) {
            sql.append(" LIMIT ?");
            params.add(limit);
        }

        try {
            return clickHouseJdbcTemplate.query(sql.toString(), 
                params.toArray(), 
                new OptimizedPrometheusMetricRowMapper());
                
        } catch (Exception e) {
            log.error("Failed to query optimized metrics from ClickHouse", e);
            throw new RuntimeException("Failed to query optimized metrics", e);
        }
    }

    /**
     * 查询优化后的指标统计信息
     */
    public List<OptimizedMetricStatistics> getOptimizedMetricStatistics(
            LocalDateTime startTime, LocalDateTime endTime, String metricName) {
        
        String sql = """
            SELECT 
                metric_name,
                job,
                app_id,
                label_set_id,
                count() as count,
                avg(value) as avg_value,
                min(value) as min_value,
                max(value) as max_value,
                sum(value) as sum_value
            FROM prometheus_metrics_optimized
            WHERE timestamp >= ? AND timestamp <= ?
            """;

        List<Object> params = List.of(Timestamp.valueOf(startTime), Timestamp.valueOf(endTime));

        if (metricName != null && !metricName.trim().isEmpty()) {
            sql += " AND metric_name = ?";
            params.add(metricName);
        }
        
        sql += " GROUP BY metric_name, job, app_id, label_set_id ORDER BY count DESC";

        try {
            return clickHouseJdbcTemplate.query(sql, 
                params.toArray(),
                new OptimizedMetricStatisticsRowMapper());
        } catch (Exception e) {
            log.error("Failed to query optimized metric statistics from ClickHouse", e);
            throw new RuntimeException("Failed to query optimized metric statistics", e);
        }
    }

    /**
     * 根据标签集合ID查询指标数据
     */
    public List<OptimizedPrometheusMetric> findMetricsByLabelSetIds(
            List<Long> labelSetIds, LocalDateTime startTime, LocalDateTime endTime, int limit) {
        
        if (labelSetIds == null || labelSetIds.isEmpty()) {
            return List.of();
        }

        StringBuilder sql = new StringBuilder("""
            SELECT timestamp, job, job_type, app_id, instance,
                   metric_name, metric_type, value, label_set_id, created_at
            FROM prometheus_metrics_optimized
            WHERE label_set_id IN (
            """);

        for (int i = 0; i < labelSetIds.size(); i++) {
            if (i > 0) sql.append(", ");
            sql.append("?");
        }
        sql.append(")");

        List<Object> params = List.of(labelSetIds.toArray());

        if (startTime != null) {
            sql.append(" AND timestamp >= ?");
            params.add(Timestamp.valueOf(startTime));
        }

        if (endTime != null) {
            sql.append(" AND timestamp <= ?");
            params.add(Timestamp.valueOf(endTime));
        }

        sql.append(" ORDER BY timestamp DESC");

        if (limit > 0) {
            sql.append(" LIMIT ?");
            params.add(limit);
        }

        try {
            return clickHouseJdbcTemplate.query(sql.toString(), 
                params.toArray(), 
                new OptimizedPrometheusMetricRowMapper());
        } catch (Exception e) {
            log.error("Failed to query metrics by label set IDs", e);
            throw new RuntimeException("Failed to query metrics by label set IDs", e);
        }
    }

    /**
     * 获取数据库统计信息
     */
    public DatabaseStats getDatabaseStats() {
        try {
            // 查询主表统计
            Map<String, Object> mainTableStats = clickHouseJdbcTemplate.queryForMap("""
                SELECT 
                    count() as total_rows,
                    uniq(metric_name) as unique_metrics,
                    uniq(job) as unique_jobs,
                    uniq(app_id) as unique_apps,
                    uniq(label_set_id) as unique_label_sets,
                    min(timestamp) as earliest_timestamp,
                    max(timestamp) as latest_timestamp
                FROM prometheus_metrics_optimized
                """);

            // 查询标签统计
            Long totalLabelSets = clickHouseJdbcTemplate.queryForObject(
                "SELECT count() FROM label_sets", Long.class);
            
            Long totalLabelKeys = clickHouseJdbcTemplate.queryForObject(
                "SELECT count() FROM label_keys", Long.class);
            
            Long totalLabelValues = clickHouseJdbcTemplate.queryForObject(
                "SELECT count() FROM label_values", Long.class);

            return DatabaseStats.builder()
                .mainTableStats(mainTableStats)
                .totalLabelSets(totalLabelSets != null ? totalLabelSets : 0L)
                .totalLabelKeys(totalLabelKeys != null ? totalLabelKeys : 0L)
                .totalLabelValues(totalLabelValues != null ? totalLabelValues : 0L)
                .build();
                
        } catch (Exception e) {
            log.error("Failed to get database stats", e);
            throw new RuntimeException("Failed to get database stats", e);
        }
    }

    // 行映射器
    private static class OptimizedPrometheusMetricRowMapper implements RowMapper<OptimizedPrometheusMetric> {
        @Override
        public OptimizedPrometheusMetric mapRow(ResultSet rs, int rowNum) throws SQLException {
            return OptimizedPrometheusMetric.builder()
                .timestamp(rs.getTimestamp("timestamp").toLocalDateTime())
                .job(rs.getString("job"))
                .jobType(rs.getString("job_type"))
                .appId(rs.getString("app_id"))
                .instance(rs.getString("instance"))
                .metricName(rs.getString("metric_name"))
                .metricType(rs.getString("metric_type"))
                .value(rs.getBigDecimal("value"))
                .labelSetId(rs.getObject("label_set_id", Long.class))
                .createdAt(rs.getTimestamp("created_at").toLocalDateTime())
                .build();
        }
    }

    // 统计信息类
    @lombok.Data
    @lombok.Builder
    public static class OptimizedMetricStatistics {
        private String metricName;
        private String job;
        private String appId;
        private Long labelSetId;
        private long count;
        private double avgValue;
        private double minValue;
        private double maxValue;
        private double sumValue;
    }

    private static class OptimizedMetricStatisticsRowMapper implements RowMapper<OptimizedMetricStatistics> {
        @Override
        public OptimizedMetricStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
            return OptimizedMetricStatistics.builder()
                .metricName(rs.getString("metric_name"))
                .job(rs.getString("job"))
                .appId(rs.getString("app_id"))
                .labelSetId(rs.getObject("label_set_id", Long.class))
                .count(rs.getLong("count"))
                .avgValue(rs.getDouble("avg_value"))
                .minValue(rs.getDouble("min_value"))
                .maxValue(rs.getDouble("max_value"))
                .sumValue(rs.getDouble("sum_value"))
                .build();
        }
    }

    @lombok.Data
    @lombok.Builder
    public static class DatabaseStats {
        private Map<String, Object> mainTableStats;
        private long totalLabelSets;
        private long totalLabelKeys;
        private long totalLabelValues;
    }
}
