package com.example.demo.dto;

import com.example.demo.dao.ClickHouseMetricsDao;
import com.example.demo.model.ClickHouseMetric;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 指标查询响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetricsQueryResponse {

    /**
     * 指标数据列表
     */
    private List<ClickHouseMetric> metrics;

    /**
     * 分页信息
     */
    private PageInfo pageInfo;

    /**
     * 统计信息（可选）
     */
    private List<ClickHouseMetricsDao.MetricStatistics> statistics;

    /**
     * 查询执行时间（毫秒）
     */
    private long executionTimeMs;

    /**
     * 分页信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PageInfo {
        /**
         * 当前页码
         */
        private int currentPage;

        /**
         * 每页大小
         */
        private int pageSize;

        /**
         * 总记录数
         */
        private long totalElements;

        /**
         * 总页数
         */
        private int totalPages;

        /**
         * 是否有下一页
         */
        private boolean hasNext;

        /**
         * 是否有上一页
         */
        private boolean hasPrevious;

        /**
         * 是否是第一页
         */
        private boolean isFirst;

        /**
         * 是否是最后一页
         */
        private boolean isLast;

        /**
         * 计算分页信息
         */
        public static PageInfo calculate(int page, int size, long totalElements) {
            int totalPages = (int) Math.ceil((double) totalElements / size);
            
            return PageInfo.builder()
                .currentPage(page)
                .pageSize(size)
                .totalElements(totalElements)
                .totalPages(totalPages)
                .hasNext(page < totalPages)
                .hasPrevious(page > 1)
                .isFirst(page == 1)
                .isLast(page == totalPages || totalPages == 0)
                .build();
        }
    }
}
