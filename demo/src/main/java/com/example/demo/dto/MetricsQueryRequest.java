package com.example.demo.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 指标查询请求DTO
 */
@Data
public class MetricsQueryRequest {

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 指标名称（可选）
     */
    private String metricName;

    /**
     * 任务名称（可选）
     */
    private String job;

    /**
     * 应用ID（可选）
     */
    private String appId;

    /**
     * 实例名称（可选）
     */
    private String instance;

    /**
     * 设备ID（可选）
     */
    private String deviceId;

    /**
     * 设备类型（可选）
     */
    private String deviceType;

    /**
     * 集群ID（可选）
     */
    private String clusterId;

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private int page = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 10000, message = "每页大小不能超过10000")
    private int size = 100;

    /**
     * 排序字段
     */
    private String sortBy = "timestamp";

    /**
     * 排序方向（ASC/DESC）
     */
    private String sortDirection = "DESC";

    /**
     * 是否包含统计信息
     */
    private boolean includeStats = false;

    /**
     * 获取偏移量
     */
    public int getOffset() {
        return (page - 1) * size;
    }

    /**
     * 获取限制数量
     */
    public int getLimit() {
        return size;
    }
}
