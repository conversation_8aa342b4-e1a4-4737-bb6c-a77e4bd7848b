package com.example.demo.controller;

import com.example.demo.dto.MetricsQueryResponse;
import com.example.demo.model.ClickHouseMetric;
import com.example.demo.service.ClickHouseMetricsService;
import com.example.demo.service.MetricsQueryService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 指标查询控制器测试
 */
@WebMvcTest(MetricsQueryController.class)
class MetricsQueryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private MetricsQueryService metricsQueryService;

    @MockBean
    private ClickHouseMetricsService clickHouseMetricsService;

    @Test
    void testQueryMetrics() throws Exception {
        // 准备测试数据
        MetricsQueryResponse response = MetricsQueryResponse.builder()
            .metrics(Arrays.asList())
            .pageInfo(MetricsQueryResponse.PageInfo.calculate(1, 100, 0))
            .executionTimeMs(100)
            .build();

        when(metricsQueryService.queryMetrics(any())).thenReturn(response);

        // 执行测试
        mockMvc.perform(get("/api/metrics/query")
                .param("startTime", "2024-01-01 00:00:00")
                .param("endTime", "2024-01-01 23:59:59")
                .param("metricName", "test_metric")
                .param("page", "1")
                .param("size", "100"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.executionTimeMs").value(100));
    }

    @Test
    void testQueryRecentMetrics() throws Exception {
        // 准备测试数据
        List<ClickHouseMetric> metrics = Arrays.asList();
        when(metricsQueryService.queryRecentMetrics(any(), any(), any(Integer.class), any(Integer.class)))
            .thenReturn(metrics);

        // 执行测试
        mockMvc.perform(get("/api/metrics/recent")
                .param("metricName", "test_metric")
                .param("job", "test_job")
                .param("hours", "24")
                .param("limit", "100"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void testQueryStatistics() throws Exception {
        // 准备测试数据
        when(metricsQueryService.queryMetricStatistics(any(), any(), any()))
            .thenReturn(Arrays.asList());

        // 执行测试
        mockMvc.perform(get("/api/metrics/statistics")
                .param("startTime", "2024-01-01 00:00:00")
                .param("endTime", "2024-01-01 23:59:59")
                .param("metricName", "test_metric"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void testQueryTopMetrics() throws Exception {
        // 准备测试数据
        when(metricsQueryService.queryTopMetrics(any(), any(), any(Integer.class)))
            .thenReturn(Arrays.asList());

        // 执行测试
        mockMvc.perform(get("/api/metrics/top")
                .param("startTime", "2024-01-01 00:00:00")
                .param("endTime", "2024-01-01 23:59:59")
                .param("limit", "10"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void testGetBufferStatus() throws Exception {
        // 准备测试数据
        ClickHouseMetricsService.BufferStatus status = 
            new ClickHouseMetricsService.BufferStatus(10, 100, 5000);
        when(clickHouseMetricsService.getBufferStatus()).thenReturn(status);

        // 执行测试
        mockMvc.perform(get("/api/metrics/buffer/status"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.currentSize").value(10))
            .andExpect(jsonPath("$.batchSize").value(100))
            .andExpect(jsonPath("$.flushInterval").value(5000));
    }

    @Test
    void testFlushBuffer() throws Exception {
        // 执行测试
        mockMvc.perform(post("/api/metrics/buffer/flush"))
            .andExpect(status().isOk())
            .andExpect(content().string("Buffer flushed successfully"));
    }

    @Test
    void testHealthCheck() throws Exception {
        // 准备测试数据
        ClickHouseMetricsService.BufferStatus status = 
            new ClickHouseMetricsService.BufferStatus(5, 100, 5000);
        when(clickHouseMetricsService.getBufferStatus()).thenReturn(status);

        // 执行测试
        mockMvc.perform(get("/api/metrics/health"))
            .andExpect(status().isOk())
            .andExpect(content().string("ClickHouse metrics service is healthy. Buffer size: 5"));
    }

    @Test
    void testQueryMetricsWithInvalidParameters() throws Exception {
        // 测试缺少必需参数
        mockMvc.perform(get("/api/metrics/query"))
            .andExpect(status().isBadRequest());

        // 测试无效的时间格式
        mockMvc.perform(get("/api/metrics/query")
                .param("startTime", "invalid-date")
                .param("endTime", "2024-01-01 23:59:59"))
            .andExpect(status().isBadRequest());
    }

    @Test
    void testQueryRecentMetricsWithInvalidHours() throws Exception {
        // 测试超出范围的小时数
        mockMvc.perform(get("/api/metrics/recent")
                .param("hours", "200")) // 超过最大值168
            .andExpect(status().isBadRequest());

        // 测试负数小时
        mockMvc.perform(get("/api/metrics/recent")
                .param("hours", "-1"))
            .andExpect(status().isBadRequest());
    }
}
