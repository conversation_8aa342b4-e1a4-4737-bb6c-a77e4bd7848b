package com.example.demo.service;

import com.example.demo.entity.PrometheusMetricEntity;
import com.example.demo.model.ClickHouseMetric;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 指标转换服务测试
 */
class MetricsConversionServiceTest {

    private MetricsConversionService conversionService;

    @BeforeEach
    void setUp() {
        conversionService = new MetricsConversionService();
    }

    @Test
    void testConvertToEntity() {
        // 准备测试数据
        ClickHouseMetric metric = createTestClickHouseMetric();

        // 执行转换
        PrometheusMetricEntity entity = conversionService.convertToEntity(metric);

        // 验证结果
        assertNotNull(entity);
        assertEquals(metric.getJob(), entity.getJob());
        assertEquals(metric.getJobType(), entity.getJobType());
        assertEquals(metric.getAppId(), entity.getAppId());
        assertEquals(metric.getName(), entity.getName());
        assertEquals(metric.getInstance(), entity.getInstance());
        assertEquals(metric.getMetricName(), entity.getMetricName());
        assertEquals(metric.getMetricType(), entity.getMetricType());
        assertEquals(metric.getDescription(), entity.getDescription());
        assertEquals(metric.getLabelsJson(), entity.getLabelsJson());
        assertEquals(metric.getValue(), entity.getValue());
        assertEquals(metric.getClusterId(), entity.getClusterId());
        assertEquals(metric.getDeviceType(), entity.getDeviceType());
        assertEquals(metric.getDeviceId(), entity.getDeviceId());
        assertEquals(metric.getIp(), entity.getIp());
        assertEquals(Integer.parseInt(metric.getPort()), entity.getPort());
        assertEquals(Integer.parseInt(metric.getConnectPort()), entity.getConnectPort());
        assertNotNull(entity.getCreatedAt());
    }

    @Test
    void testConvertToEntityWithNullValues() {
        // 准备测试数据（包含null值）
        ClickHouseMetric metric = ClickHouseMetric.builder()
            .timestamp(Instant.now())
            .metricName("test_metric")
            .value(new BigDecimal("100"))
            .port(null)
            .connectPort(null)
            .build();

        // 执行转换
        PrometheusMetricEntity entity = conversionService.convertToEntity(metric);

        // 验证结果
        assertNotNull(entity);
        assertEquals(metric.getMetricName(), entity.getMetricName());
        assertEquals(metric.getValue(), entity.getValue());
        assertNull(entity.getPort());
        assertNull(entity.getConnectPort());
    }

    @Test
    void testConvertFromEntity() {
        // 准备测试数据
        PrometheusMetricEntity entity = createTestPrometheusMetricEntity();

        // 执行转换
        ClickHouseMetric metric = conversionService.convertFromEntity(entity);

        // 验证结果
        assertNotNull(metric);
        assertEquals(entity.getJob(), metric.getJob());
        assertEquals(entity.getJobType(), metric.getJobType());
        assertEquals(entity.getAppId(), metric.getAppId());
        assertEquals(entity.getName(), metric.getName());
        assertEquals(entity.getInstance(), metric.getInstance());
        assertEquals(entity.getMetricName(), metric.getMetricName());
        assertEquals(entity.getMetricType(), metric.getMetricType());
        assertEquals(entity.getDescription(), metric.getDescription());
        assertEquals(entity.getLabelsJson(), metric.getLabelsJson());
        assertEquals(entity.getValue(), metric.getValue());
        assertEquals(entity.getClusterId(), metric.getClusterId());
        assertEquals(entity.getDeviceType(), metric.getDeviceType());
        assertEquals(entity.getDeviceId(), metric.getDeviceId());
        assertEquals(entity.getIp(), metric.getIp());
        assertEquals(entity.getPort().toString(), metric.getPort());
        assertEquals(entity.getConnectPort().toString(), metric.getConnectPort());
    }

    @Test
    void testConvertToEntities() {
        // 准备测试数据
        List<ClickHouseMetric> metrics = Arrays.asList(
            createTestClickHouseMetric("metric1"),
            createTestClickHouseMetric("metric2")
        );

        // 执行转换
        List<PrometheusMetricEntity> entities = conversionService.convertToEntities(metrics);

        // 验证结果
        assertNotNull(entities);
        assertEquals(2, entities.size());
        assertEquals("metric1", entities.get(0).getMetricName());
        assertEquals("metric2", entities.get(1).getMetricName());
    }

    @Test
    void testConvertFromEntities() {
        // 准备测试数据
        List<PrometheusMetricEntity> entities = Arrays.asList(
            createTestPrometheusMetricEntity("metric1"),
            createTestPrometheusMetricEntity("metric2")
        );

        // 执行转换
        List<ClickHouseMetric> metrics = conversionService.convertFromEntities(entities);

        // 验证结果
        assertNotNull(metrics);
        assertEquals(2, metrics.size());
        assertEquals("metric1", metrics.get(0).getMetricName());
        assertEquals("metric2", metrics.get(1).getMetricName());
    }

    @Test
    void testIsValidMetric() {
        // 测试有效指标
        ClickHouseMetric validMetric = createTestClickHouseMetric();
        assertTrue(conversionService.isValidMetric(validMetric));

        // 测试无效指标 - null
        assertFalse(conversionService.isValidMetric(null));

        // 测试无效指标 - 缺少指标名称
        ClickHouseMetric invalidMetric1 = ClickHouseMetric.builder()
            .timestamp(Instant.now())
            .value(new BigDecimal("100"))
            .build();
        assertFalse(conversionService.isValidMetric(invalidMetric1));

        // 测试无效指标 - 缺少值
        ClickHouseMetric invalidMetric2 = ClickHouseMetric.builder()
            .timestamp(Instant.now())
            .metricName("test_metric")
            .build();
        assertFalse(conversionService.isValidMetric(invalidMetric2));

        // 测试无效指标 - 缺少时间戳
        ClickHouseMetric invalidMetric3 = ClickHouseMetric.builder()
            .metricName("test_metric")
            .value(new BigDecimal("100"))
            .build();
        assertFalse(conversionService.isValidMetric(invalidMetric3));
    }

    @Test
    void testFilterValidMetrics() {
        // 准备测试数据
        List<ClickHouseMetric> metrics = Arrays.asList(
            createTestClickHouseMetric("valid_metric1"),
            ClickHouseMetric.builder().build(), // 无效指标
            createTestClickHouseMetric("valid_metric2"),
            null // null指标
        );

        // 执行过滤
        List<ClickHouseMetric> validMetrics = conversionService.filterValidMetrics(metrics);

        // 验证结果
        assertNotNull(validMetrics);
        assertEquals(2, validMetrics.size());
        assertEquals("valid_metric1", validMetrics.get(0).getMetricName());
        assertEquals("valid_metric2", validMetrics.get(1).getMetricName());
    }

    /**
     * 创建测试ClickHouseMetric
     */
    private ClickHouseMetric createTestClickHouseMetric() {
        return createTestClickHouseMetric("test_metric");
    }

    /**
     * 创建测试ClickHouseMetric
     */
    private ClickHouseMetric createTestClickHouseMetric(String metricName) {
        return ClickHouseMetric.builder()
            .timestamp(Instant.now())
            .job("test_job")
            .jobType("test")
            .appId("test_app")
            .name("test_name")
            .instance("test_instance")
            .metricName(metricName)
            .metricType("gauge")
            .description("Test metric")
            .labelsJson("{\"label1\":\"value1\"}")
            .value(new BigDecimal("123.45"))
            .clusterId("cluster1")
            .deviceType("server")
            .deviceId("device1")
            .ip("***********")
            .port("8080")
            .connectPort("9090")
            .build();
    }

    /**
     * 创建测试PrometheusMetricEntity
     */
    private PrometheusMetricEntity createTestPrometheusMetricEntity() {
        return createTestPrometheusMetricEntity("test_metric");
    }

    /**
     * 创建测试PrometheusMetricEntity
     */
    private PrometheusMetricEntity createTestPrometheusMetricEntity(String metricName) {
        return PrometheusMetricEntity.builder()
            .timestamp(LocalDateTime.now())
            .job("test_job")
            .jobType("test")
            .appId("test_app")
            .name("test_name")
            .instance("test_instance")
            .metricName(metricName)
            .metricType("gauge")
            .description("Test metric")
            .labelsJson("{\"label1\":\"value1\"}")
            .value(new BigDecimal("123.45"))
            .clusterId("cluster1")
            .deviceType("server")
            .deviceId("device1")
            .ip("***********")
            .port(8080)
            .connectPort(9090)
            .createdAt(LocalDateTime.now())
            .build();
    }
}
