package com.example.demo.service;

import com.example.demo.config.ClickHouseProperties;
import com.example.demo.dao.ClickHouseMetricsDao;
import com.example.demo.model.ClickHouseMetric;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ClickHouse指标服务测试
 */
@ExtendWith(MockitoExtension.class)
class ClickHouseMetricsServiceTest {

    @Mock
    private ClickHouseMetricsDao clickHouseMetricsDao;

    @Mock
    private MetricsConversionService conversionService;

    private ClickHouseProperties clickHouseProperties;
    private ClickHouseMetricsService clickHouseMetricsService;

    @BeforeEach
    void setUp() {
        clickHouseProperties = new ClickHouseProperties();
        clickHouseProperties.getBatch().setSize(10);
        clickHouseProperties.getBatch().setTimeout(1000);

        clickHouseMetricsService = new ClickHouseMetricsService(
            clickHouseMetricsDao, conversionService, clickHouseProperties);
    }

    @Test
    void testAddValidMetric() {
        // 准备测试数据
        ClickHouseMetric metric = createTestMetric();
        
        // 模拟验证通过
        when(conversionService.isValidMetric(metric)).thenReturn(true);

        // 执行测试
        clickHouseMetricsService.addMetric(metric);

        // 验证缓冲区状态
        ClickHouseMetricsService.BufferStatus status = clickHouseMetricsService.getBufferStatus();
        assertEquals(1, status.getCurrentSize());
    }

    @Test
    void testAddInvalidMetric() {
        // 准备测试数据
        ClickHouseMetric metric = createTestMetric();
        
        // 模拟验证失败
        when(conversionService.isValidMetric(metric)).thenReturn(false);

        // 执行测试
        clickHouseMetricsService.addMetric(metric);

        // 验证缓冲区状态
        ClickHouseMetricsService.BufferStatus status = clickHouseMetricsService.getBufferStatus();
        assertEquals(0, status.getCurrentSize());
    }

    @Test
    void testBatchInsert() {
        // 准备测试数据
        List<ClickHouseMetric> metrics = Arrays.asList(
            createTestMetric("metric1"),
            createTestMetric("metric2"),
            createTestMetric("metric3")
        );

        // 模拟转换服务
        when(conversionService.filterValidMetrics(metrics)).thenReturn(metrics);
        when(conversionService.convertToEntities(metrics)).thenReturn(Arrays.asList());

        // 执行测试
        clickHouseMetricsService.insertMetricsImmediately(metrics);

        // 验证调用
        verify(conversionService).filterValidMetrics(metrics);
        verify(conversionService).convertToEntities(metrics);
        verify(clickHouseMetricsDao).batchInsertMetrics(any());
    }

    @Test
    void testBufferFlushWhenFull() {
        // 设置小的批量大小
        clickHouseProperties.getBatch().setSize(2);

        // 准备测试数据
        ClickHouseMetric metric1 = createTestMetric("metric1");
        ClickHouseMetric metric2 = createTestMetric("metric2");

        // 模拟验证通过
        when(conversionService.isValidMetric(any())).thenReturn(true);
        when(conversionService.filterValidMetrics(any())).thenReturn(Arrays.asList(metric1, metric2));
        when(conversionService.convertToEntities(any())).thenReturn(Arrays.asList());

        // 添加指标，应该触发自动刷新
        clickHouseMetricsService.addMetric(metric1);
        clickHouseMetricsService.addMetric(metric2);

        // 验证批量插入被调用
        verify(clickHouseMetricsDao, timeout(1000)).batchInsertMetrics(any());
    }

    @Test
    void testGetBufferStatus() {
        ClickHouseMetricsService.BufferStatus status = clickHouseMetricsService.getBufferStatus();
        
        assertNotNull(status);
        assertEquals(0, status.getCurrentSize());
        assertEquals(clickHouseProperties.getBatch().getSize(), status.getBatchSize());
        assertEquals(clickHouseProperties.getBatch().getTimeout(), status.getFlushInterval());
    }

    @Test
    void testForceFlushBuffer() {
        // 添加一些指标到缓冲区
        ClickHouseMetric metric = createTestMetric();
        when(conversionService.isValidMetric(metric)).thenReturn(true);
        clickHouseMetricsService.addMetric(metric);

        // 模拟转换
        when(conversionService.filterValidMetrics(any())).thenReturn(Arrays.asList(metric));
        when(conversionService.convertToEntities(any())).thenReturn(Arrays.asList());

        // 强制刷新
        clickHouseMetricsService.forceFlushBuffer();

        // 验证批量插入被调用
        verify(clickHouseMetricsDao).batchInsertMetrics(any());
    }

    /**
     * 创建测试指标
     */
    private ClickHouseMetric createTestMetric() {
        return createTestMetric("test_metric");
    }

    /**
     * 创建测试指标
     */
    private ClickHouseMetric createTestMetric(String metricName) {
        return ClickHouseMetric.builder()
            .timestamp(Instant.now())
            .job("test_job")
            .jobType("test")
            .appId("test_app")
            .name("test_name")
            .instance("test_instance")
            .metricName(metricName)
            .metricType("gauge")
            .description("Test metric")
            .labelsJson("{\"label1\":\"value1\"}")
            .value(new BigDecimal("123.45"))
            .clusterId("cluster1")
            .deviceType("server")
            .deviceId("device1")
            .ip("***********")
            .port("8080")
            .connectPort("9090")
            .build();
    }
}
